<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洗牌开场接口测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status-panel {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected {
            background-color: #28a745;
        }
        .status-disconnected {
            background-color: #dc3545;
        }
        .control-panel {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
        }
        .section h2 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .form-inline {
            display: flex;
            gap: 10px;
            align-items: end;
        }
        .form-inline .form-group {
            flex: 1;
            margin-bottom: 0;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            background-color: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            word-wrap: break-word;
        }
        .message-send {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .message-receive {
            background-color: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }
        .message-error {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
        }
        .message-success {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .timestamp {
            color: #666;
            font-size: 11px;
            margin-right: 10px;
        }
        .user-info {
            background-color: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
        }
        .user-info h3 {
            margin-top: 0;
            color: #2e7d32;
        }
        .shuffle-info {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
        }
        .shuffle-info h3 {
            margin-top: 0;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>洗牌开场接口测试工具</h1>
        
        <!-- 连接状态 -->
        <div class="status-panel">
            <span class="status-indicator" id="status-indicator"></span>
            <span id="connection-status">未连接</span>
            <span style="margin-left: 20px;">客户端ID: <span id="client-id">-</span></span>
        </div>

        <!-- 连接控制 -->
        <div class="control-panel">
            <button class="btn btn-primary" onclick="connect()">连接</button>
            <button class="btn btn-danger" onclick="disconnect()">断开连接</button>
            <button class="btn btn-warning" onclick="clearMessages()">清空消息</button>
        </div>

        <!-- 用户登录 -->
        <div class="section">
            <h2>用户登录</h2>
            <div class="form-inline">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" value="admin" placeholder="请输入用户名">
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" value="123456" placeholder="请输入密码">
                </div>
                <div class="form-group">
                    <button class="btn btn-success" onclick="login()">登录</button>
                </div>
            </div>
            <div id="user-info-display"></div>
        </div>

        <!-- 洗牌开场 -->
        <div class="section">
            <h2>洗牌开场</h2>
            
            <!-- 基本信息 -->
            <div class="form-row">
                <div class="form-group">
                    <label for="shuffle-account-period">账期</label>
                    <input type="date" id="shuffle-account-period" placeholder="选择账期">
                </div>
                <div class="form-group">
                    <label style="color: #666; font-size: 12px;">桌台ID将通过IP自动获取</label>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="shuffle-shift">班次</label>
                    <select id="shuffle-shift">
                        <option value="1">早班</option>
                        <option value="2">晚班</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="shuffle-method">洗牌方式</label>
                    <input type="text" id="shuffle-method" placeholder="请输入洗牌方式">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="card-color">牌色</label>
                    <input type="text" id="card-color" placeholder="请输入牌色">
                </div>
                <div class="form-group">
                    <label for="monitor-id">监场人员ID</label>
                    <input type="text" id="monitor-id" placeholder="请输入监场人员ID">
                </div>
            </div>
            
            <!-- 人员信息 -->
            <div class="form-row">
                <div class="form-group">
                    <label for="monitor-name">监场人员名称</label>
                    <input type="text" id="monitor-name" placeholder="请输入监场人员名称">
                </div>
                <div class="form-group">
                    <label for="admin-id">管理员ID</label>
                    <input type="text" id="admin-id" placeholder="请输入管理员ID">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="admin-name">管理员名称</label>
                    <input type="text" id="admin-name" placeholder="请输入管理员名称">
                </div>
                <div class="form-group">
                    <label for="shuffle-table-poker">洗牌卓牌手</label>
                    <input type="text" id="shuffle-table-poker" placeholder="请输入洗牌卓牌手">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="table-poker">台面洗牌牌手</label>
                    <input type="text" id="table-poker" placeholder="请输入台面洗牌牌手">
                </div>
                <div class="form-group">
                    <label for="monitor-poker">监管洗牌手</label>
                    <input type="text" id="monitor-poker" placeholder="请输入监管洗牌手">
                </div>
            </div>
            
            <div class="form-group">
                <label for="cut-card-dealer">切牌人</label>
                <input type="text" id="cut-card-dealer" placeholder="请输入切牌人">
            </div>
            
            <button class="btn btn-success" onclick="startShuffle()">开始洗牌</button>
        </div>

        <!-- 洗牌结果显示 -->
        <div id="shuffle-result-display"></div>

        <!-- 消息显示 -->
        <div class="section">
            <h2>消息记录</h2>
            <div class="messages" id="messages"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let isLoggedIn = false;
        let token = '';

        // 设置默认账期为今天
        document.getElementById('shuffle-account-period').value = new Date().toISOString().split('T')[0];

        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                addMessage('已经连接到服务器', 'error');
                return;
            }

            ws = new WebSocket('ws://************:8080/ws');
            
            ws.onopen = function(event) {
                updateConnectionStatus(true);
                addMessage('WebSocket连接已建立', 'success');
            };

            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                handleMessage(message);
            };

            ws.onerror = function(error) {
                addMessage('WebSocket错误: ' + JSON.stringify(error), 'error');
            };

            ws.onclose = function(event) {
                updateConnectionStatus(false);
                addMessage('WebSocket连接已关闭', 'error');
                isLoggedIn = false;
                token = '';
                document.getElementById('user-info-display').innerHTML = '';
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        function updateConnectionStatus(connected) {
            const indicator = document.getElementById('status-indicator');
            const status = document.getElementById('connection-status');
            
            if (connected) {
                indicator.className = 'status-indicator status-connected';
                status.textContent = '已连接';
            } else {
                indicator.className = 'status-indicator status-disconnected';
                status.textContent = '未连接';
                document.getElementById('client-id').textContent = '-';
            }
        }

        function login() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('请先连接到服务器', 'error');
                return;
            }

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                addMessage('请输入用户名和密码', 'error');
                return;
            }

            const message = {
                type: 'login',
                data: {
                    username: username,
                    password: password
                }
            };

            sendMessage(message);
        }

        function startShuffle() {
            if (!isLoggedIn) {
                addMessage('请先登录', 'error');
                return;
            }

            const accountPeriod = document.getElementById('shuffle-account-period').value;
            const shift = parseInt(document.getElementById('shuffle-shift').value);
            
            if (!accountPeriod || !shift) {
                addMessage('请填写账期和班次', 'error');
                return;
            }

            const message = {
                type: 'start_shuffle',
                data: {
                    account_period: accountPeriod,
                    shift: shift,
                    shuffle_method: document.getElementById('shuffle-method').value,
                    card_color: document.getElementById('card-color').value,
                    monitor_id: document.getElementById('monitor-id').value,
                    monitor_name: document.getElementById('monitor-name').value,
                    admin_id: document.getElementById('admin-id').value,
                    admin_name: document.getElementById('admin-name').value,
                    shuffle_table_poker: document.getElementById('shuffle-table-poker').value,
                    table_poker: document.getElementById('table-poker').value,
                    monitor_poker: document.getElementById('monitor-poker').value,
                    cut_card_dealer: document.getElementById('cut-card-dealer').value
                }
            };

            sendMessage(message);
        }

        function sendMessage(message) {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('WebSocket未连接', 'error');
                return;
            }

            ws.send(JSON.stringify(message));
            addMessage('发送: ' + JSON.stringify(message, null, 2), 'send');
        }

        function handleMessage(message) {
            addMessage('接收: ' + JSON.stringify(message, null, 2), 'receive');

            switch(message.type) {
                case 'login_success':
                    isLoggedIn = true;
                    token = message.data.token;
                    document.getElementById('client-id').textContent = message.to || 'unknown';
                    displayUserInfo(message.data.user_info);
                    break;
                    
                case 'login_error':
                    addMessage('登录失败: ' + message.data.error, 'error');
                    break;
                    
                case 'start_shuffle_success':
                    addMessage('洗牌开场成功!', 'success');
                    displayShuffleResult(message.data);
                    console.log('洗牌结果:', message.data);
                    break;
                    
                case 'start_shuffle_error':
                    addMessage('洗牌开场失败: ' + message.data.error, 'error');
                    break;
            }
        }

        function displayUserInfo(userInfo) {
            const html = `
                <div class="user-info">
                    <h3>用户信息</h3>
                    <p><strong>用户名:</strong> ${userInfo.username}</p>
                    <p><strong>真实姓名:</strong> ${userInfo.realname}</p>
                    <p><strong>昵称:</strong> ${userInfo.nickname}</p>
                    <p><strong>状态:</strong> ${userInfo.status_name}</p>
                    <p><strong>登录时间:</strong> ${new Date(userInfo.login_time * 1000).toLocaleString()}</p>
                </div>
            `;
            document.getElementById('user-info-display').innerHTML = html;
        }

        function displayShuffleResult(data) {
            const html = `
                <div class="section">
                    <h2>洗牌开场结果</h2>
                    <div class="shuffle-info">
                        <h3>洗牌记录</h3>
                        <p><strong>记录ID:</strong> ${data.shuffle_record.id}</p>
                        <p><strong>桌台ID:</strong> ${data.shuffle_record.table_id}</p>
                        <p><strong>账期:</strong> ${data.shuffle_record.account_period}</p>
                        <p><strong>场次编号:</strong> ${data.shuffle_record.shoe_no}</p>
                        <p><strong>牌次号:</strong> ${data.shuffle_record.card_no}</p>
                        <p><strong>班次:</strong> ${data.shuffle_record.shift_name}</p>
                        <p><strong>洗牌方式:</strong> ${data.shuffle_record.shuffle_method}</p>
                        <p><strong>牌色:</strong> ${data.shuffle_record.card_color}</p>
                        <p><strong>监场人员:</strong> ${data.shuffle_record.monitor_name}</p>
                        <p><strong>管理员:</strong> ${data.shuffle_record.admin_name}</p>
                        <p><strong>创建时间:</strong> ${data.shuffle_record.create_time}</p>
                        
                        <h3>桌台状态</h3>
                        <p><strong>状态:</strong> ${data.tables_start.stats_name}</p>
                        <p><strong>场次编号:</strong> ${data.tables_start.shoe_no}</p>
                        <p><strong>局号编号:</strong> ${data.tables_start.hand_no}</p>
                        <p><strong>游戏类型:</strong> ${data.tables_start.game_type_name}</p>
                    </div>
                </div>
            `;
            document.getElementById('shuffle-result-display').innerHTML = html;
        }

        function addMessage(content, type) {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message message-' + type;
            
            const timestamp = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `<span class="timestamp">${timestamp}</span>${content}`;
            
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        // 页面加载完成后自动连接
        window.onload = function() {
            connect();
        };
    </script>
</body>
</html> 