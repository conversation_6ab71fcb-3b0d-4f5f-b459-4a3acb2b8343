<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>桌台统计接口测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="date"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .disconnect {
            background-color: #dc3545;
        }
        .disconnect:hover {
            background-color: #c82333;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .response {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            max-height: 500px;
            overflow-y: auto;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .stats-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }
        .stats-card h3 {
            margin: 0 0 15px 0;
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .stats-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .stats-item:last-child {
            border-bottom: none;
        }
        .stats-label {
            font-weight: bold;
            color: #495057;
        }
        .stats-value {
            color: #007bff;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>桌台统计接口测试</h1>
        
        <div class="form-group">
            <label for="serverUrl">服务器地址:</label>
            <input type="text" id="serverUrl" value="ws://localhost:8080/ws">
        </div>
        
        <div class="form-group">
            <label for="accountPeriod">账期 (YYYY-MM-DD):</label>
            <input type="date" id="accountPeriod" value="">
        </div>
        
        <button onclick="connectWebSocket()">连接</button>
        <button onclick="disconnectWebSocket()" class="disconnect">断开连接</button>
        <button onclick="getTableStats()">获取桌台统计</button>
        
        <div id="status" class="status disconnected">未连接</div>
        
        <div id="response" class="response"></div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;

        // 设置默认日期为今天
        document.getElementById('accountPeriod').value = new Date().toISOString().split('T')[0];

        function updateStatus(message, isConnected) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + (isConnected ? 'connected' : 'disconnected');
        }

        function logMessage(message) {
            const responseDiv = document.getElementById('response');
            const timestamp = new Date().toLocaleTimeString();
            responseDiv.textContent += `[${timestamp}] ${message}\n`;
            responseDiv.scrollTop = responseDiv.scrollHeight;
        }

        function connectWebSocket() {
            if (isConnected) {
                logMessage('已经连接，无需重复连接');
                return;
            }

            const serverUrl = document.getElementById('serverUrl').value;
            
            try {
                ws = new WebSocket(serverUrl);
                
                ws.onopen = function(event) {
                    isConnected = true;
                    updateStatus('已连接', true);
                    logMessage('WebSocket连接成功');
                };
                
                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        logMessage('收到消息: ' + JSON.stringify(data, null, 2));
                        
                        // 处理桌台统计响应
                        if (data.type === 'get_table_stats_success') {
                            displayTableStats(data.data);
                        }
                    } catch (e) {
                        logMessage('解析消息失败: ' + event.data);
                    }
                };
                
                ws.onclose = function(event) {
                    isConnected = false;
                    updateStatus('连接已断开', false);
                    logMessage('WebSocket连接已断开');
                };
                
                ws.onerror = function(error) {
                    logMessage('WebSocket错误: ' + error);
                };
                
            } catch (e) {
                logMessage('连接失败: ' + e.message);
            }
        }

        function disconnectWebSocket() {
            if (ws && isConnected) {
                ws.close();
            }
        }

        function getTableStats() {
            if (!isConnected) {
                alert('请先连接WebSocket');
                return;
            }

            const accountPeriod = document.getElementById('accountPeriod').value;
            
            if (!accountPeriod) {
                alert('请输入账期');
                return;
            }

            const message = {
                type: 'get_table_stats',
                data: {
                    account_period: accountPeriod
                }
            };

            ws.send(JSON.stringify(message));
            logMessage('发送获取桌台统计请求: ' + JSON.stringify(message, null, 2));
        }

        function displayTableStats(stats) {
            // 创建或更新统计显示区域
            let statsContainer = document.getElementById('statsContainer');
            if (!statsContainer) {
                statsContainer = document.createElement('div');
                statsContainer.id = 'statsContainer';
                statsContainer.className = 'stats-grid';
                document.querySelector('.container').appendChild(statsContainer);
            }

            statsContainer.innerHTML = '';

            // 基本信息卡片
            const basicCard = document.createElement('div');
            basicCard.className = 'stats-card';
            basicCard.innerHTML = `
                <h3>基本信息</h3>
                <div class="stats-item">
                    <span class="stats-label">桌台ID:</span>
                    <span class="stats-value">${stats.table_id}</span>
                </div>
                <div class="stats-item">
                    <span class="stats-label">桌台编号:</span>
                    <span class="stats-value">${stats.table_code}</span>
                </div>
                <div class="stats-item">
                    <span class="stats-label">账期:</span>
                    <span class="stats-value">${stats.account_period}</span>
                </div>
            `;
            statsContainer.appendChild(basicCard);

            // 洗牌信息卡片
            if (stats.shuffle_info) {
                const shuffleCard = document.createElement('div');
                shuffleCard.className = 'stats-card';
                shuffleCard.innerHTML = `
                    <h3>最新洗牌信息</h3>
                    <div class="stats-item">
                        <span class="stats-label">洗牌方式:</span>
                        <span class="stats-value">${stats.shuffle_info.shuffle_method || '暂无'}</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">台面洗牌牌手:</span>
                        <span class="stats-value">${stats.shuffle_info.table_poker || '暂无'}</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">切牌人:</span>
                        <span class="stats-value">${stats.shuffle_info.cut_card_dealer || '暂无'}</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">场次编号:</span>
                        <span class="stats-value">${stats.shuffle_info.shoe_no || 0}</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">牌次号:</span>
                        <span class="stats-value">${stats.shuffle_info.card_no || 0}</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">创建时间:</span>
                        <span class="stats-value">${stats.shuffle_info.create_time || '暂无'}</span>
                    </div>
                `;
                statsContainer.appendChild(shuffleCard);
            }

            // 财务统计卡片
            if (stats.finance_stats) {
                Object.keys(stats.finance_stats).forEach(currencyType => {
                    const financeData = stats.finance_stats[currencyType];
                    const financeCard = document.createElement('div');
                    financeCard.className = 'stats-card';
                    financeCard.innerHTML = `
                        <h3>财务统计 - ${financeData.currency_type_name}</h3>
                        <div class="stats-item">
                            <span class="stats-label">出码量:</span>
                            <span class="stats-value">${financeData.out_code_amount.toFixed(2)}</span>
                        </div>
                        <div class="stats-item">
                            <span class="stats-label">加彩量:</span>
                            <span class="stats-value">${financeData.add_code_amount.toFixed(2)}</span>
                        </div>
                    `;
                    statsContainer.appendChild(financeCard);
                });
            }

            // 手牌统计卡片
            if (stats.hand_stats) {
                Object.keys(stats.hand_stats).forEach(currencyType => {
                    const handData = stats.hand_stats[currencyType];
                    const handCard = document.createElement('div');
                    handCard.className = 'stats-card';
                    handCard.innerHTML = `
                        <h3>手牌统计 - ${handData.currency_type_name}</h3>
                        <div class="stats-item">
                            <span class="stats-label">本期洗码量合计:</span>
                            <span class="stats-value">${handData.total_wash_amount.toFixed(2)}</span>
                        </div>
                        <div class="stats-item">
                            <span class="stats-label">小费合计:</span>
                            <span class="stats-value">${handData.total_tip_amount.toFixed(2)}</span>
                        </div>
                        <div class="stats-item">
                            <span class="stats-label">输赢金额合计:</span>
                            <span class="stats-value">${handData.total_win_loss.toFixed(2)}</span>
                        </div>
                        <div class="stats-item">
                            <span class="stats-label">最新一局输赢:</span>
                            <span class="stats-value">${handData.latest_win_loss.toFixed(2)}</span>
                        </div>
                        <div class="stats-item">
                            <span class="stats-label">总码量:</span>
                            <span class="stats-value">${handData.total_code_amount.toFixed(2)}</span>
                        </div>
                    `;
                    statsContainer.appendChild(handCard);
                });
            }
        }

        // 页面加载时自动连接
        window.onload = function() {
            // 可选择是否自动连接
            // connectWebSocket();
        };
    </script>
</body>
</html> 