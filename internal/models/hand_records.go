package models

import (
	"time"
)

// HandRecord 条口记录
type HandRecord struct {
	ID            int64     `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	TableID       int64     `json:"table_id" gorm:"not null;comment:桌台ID"`
	AccountPeriod string    `json:"account_period" gorm:"type:varchar(32);default:'';not null;comment:账期"`
	ShoeNo        int       `json:"shoe_no" gorm:"not null;comment:场次编号"`
	HandNo        int       `json:"hand_no" gorm:"not null;comment:局号编号"`
	GameType      int8      `json:"game_type" gorm:"type:tinyint(1);comment:游戏类型:1-百家乐;2-龙虎斗;3-百家乐免佣;4-牛牛;5-三公;6-A89;7-庄闲牛;8-骰宝;-轮盘;"`
	CurrencyType  int8      `json:"currency_type" gorm:"type:tinyint(1);comment:货币类型:1-筹码;2-现金;3-U码;"`
	BetAmount     float64   `json:"bet_amount" gorm:"type:decimal(12,2);not null;comment:下注金额"`
	Result1       string    `json:"result1" gorm:"type:varchar(64);comment:结果区域（庄、闲等）"`
	WinLoss       float64   `json:"win_loss" gorm:"type:decimal(12,2);comment:输赢金额（正数为赢，负数为输）"`
	Loss          float64   `json:"loss" gorm:"type:decimal(12,2);comment:输口"`
	AmountTip     float64   `json:"amount_tip" gorm:"type:decimal(12,2);default:0.00;comment:小费金额"`
	AmountBottom  float64   `json:"amount_bottom" gorm:"type:decimal(12,2);default:0.00;comment:和底"`
	WashRate      float64   `json:"wash_rate" gorm:"type:decimal(5,4);comment:洗码率"`
	WashAmount    float64   `json:"wash_amount" gorm:"type:decimal(12,2);default:0.00;comment:本局洗码量"`
	WashTip       float64   `json:"wash_tip" gorm:"type:decimal(12,2);default:0.00;comment:洗码费"`
	CreateTime    time.Time `json:"create_time" gorm:"default:CURRENT_TIMESTAMP;comment:创建时间"`
}

// TableName 设置表名
func (HandRecord) TableName() string {
	return "hand_records"
}
