package services

import (
	"accounting_enter/internal/database"
	"accounting_enter/internal/models"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// TableService 桌台服务
type TableService struct{}

// NewTableService 创建桌台服务实例
func NewTableService() *TableService {
	return &TableService{}
}

// GetTableByIP 通过IP获取桌台信息
func (s *TableService) GetTableByIP(ip string) (*models.Table, error) {
	if ip == "" {
		return nil, errors.New("IP地址不能为空")
	}

	var table models.Table
	err := database.DB.Where("table_ip = ? AND status = ?", ip, 2).First(&table).Error
	if err != nil {
		if errors.Is(err, database.DB.Error) {
			return nil, fmt.Errorf("未找到IP为 %s 的启用桌台", ip)
		}
		return nil, fmt.Errorf("查询桌台信息失败: %v", err)
	}

	return &table, nil
}

// GetTableWithBets 获取桌台信息及其下注配置
func (s *TableService) GetTableWithBets(ip string) (*models.Table, []models.TableBet, error) {
	// 先获取桌台信息
	table, err := s.GetTableByIP(ip)
	if err != nil {
		return nil, nil, err
	}

	// 获取该桌台的下注配置
	var bets []models.TableBet
	err = database.DB.Where("table_id = ?", table.ID).Find(&bets).Error
	if err != nil {
		return nil, nil, fmt.Errorf("查询桌台下注配置失败: %v", err)
	}

	// 缓存桌台信息到Redis
	err = s.CacheTableInfoToRedis(ip, table)
	if err != nil {
		// 缓存失败不影响主要功能，只记录日志
		fmt.Printf("缓存桌台信息到Redis失败: %v\n", err)
	}

	return table, bets, nil
}

// CacheTableInfoToRedis 将桌台信息缓存到Redis
func (s *TableService) CacheTableInfoToRedis(ip string, table *models.Table) error {
	ctx := context.Background()

	// 构造Redis键名：table_info:{ip}
	redisKey := fmt.Sprintf("table_info:%s", ip)

	// 构造缓存数据结构
	cacheData := map[string]interface{}{
		"table_id":   table.ID,
		"table_code": table.TableCode,
		"table_name": table.Name,
		"game_type":  table.GameType,
		"table_ip":   table.TableIP,
		"status":     table.Status,
		"cache_time": time.Now().Unix(),
	}

	// 序列化为JSON
	cacheJSON, err := json.Marshal(cacheData)
	if err != nil {
		return fmt.Errorf("序列化桌台信息失败: %v", err)
	}

	// 保存到Redis，设置过期时间为1小时
	err = database.RedisClient.Set(ctx, redisKey, cacheJSON, time.Hour).Err()
	if err != nil {
		return fmt.Errorf("保存桌台信息到Redis失败: %v", err)
	}

	return nil
}

// GetTableInfoFromRedis 从Redis获取桌台信息
func (s *TableService) GetTableInfoFromRedis(ip string) (map[string]interface{}, error) {
	ctx := context.Background()
	redisKey := fmt.Sprintf("table_info:%s", ip)

	cacheJSON, err := database.RedisClient.Get(ctx, redisKey).Result()
	if err != nil {
		return nil, fmt.Errorf("从Redis获取桌台信息失败: %v", err)
	}

	var cacheData map[string]interface{}
	err = json.Unmarshal([]byte(cacheJSON), &cacheData)
	if err != nil {
		return nil, fmt.Errorf("反序列化桌台信息失败: %v", err)
	}

	return cacheData, nil
}

// GetAllActiveTables 获取所有启用的桌台
func (s *TableService) GetAllActiveTables() ([]models.Table, error) {
	var tables []models.Table
	err := database.DB.Where("status = ?", 2).Find(&tables).Error
	if err != nil {
		return nil, fmt.Errorf("查询启用桌台失败: %v", err)
	}
	return tables, nil
}

// TableResponse 桌台信息响应结构
type TableResponse struct {
	ID           int               `json:"id"`
	TableCode    string            `json:"table_code"`
	Name         string            `json:"table_name"`
	GameType     int8              `json:"game_type"`
	GameTypeName string            `json:"game_type_name"`
	TableIP      string            `json:"table_ip"`
	VideoURL     string            `json:"video_url"`
	Channel      int8              `json:"channel"`
	ChannelName  string            `json:"channel_name"`
	WashUserType string            `json:"wash_user_type"`
	WashRate     float64           `json:"wash_rate"`
	MaxBetU      float64           `json:"max_bet_u"`
	MaxBetCash   float64           `json:"max_bet_cash"`
	MaxBetChips  float64           `json:"max_bet_chips"`
	TieRate      float64           `json:"tie_rate"`
	Status       int8              `json:"status"`
	StatusName   string            `json:"status_name"`
	Memo         string            `json:"memo"`
	Bets         []models.TableBet `json:"bets,omitempty"`
	CreateTime   string            `json:"create_time"`
}

// FormatTableResponse 格式化桌台响应数据
func (s *TableService) FormatTableResponse(table *models.Table, bets []models.TableBet) *TableResponse {
	response := &TableResponse{
		ID:           table.ID,
		TableCode:    table.TableCode,
		Name:         table.Name,
		GameType:     table.GameType,
		GameTypeName: s.getGameTypeName(table.GameType),
		TableIP:      table.TableIP,
		VideoURL:     table.VideoURL,
		Channel:      table.Channel,
		ChannelName:  s.getChannelName(table.Channel),
		WashUserType: table.WashUserType,
		WashRate:     table.WashRate,
		MaxBetU:      table.MaxBetU,
		MaxBetCash:   table.MaxBetCash,
		MaxBetChips:  table.MaxBetChips,
		TieRate:      table.TieRate,
		Status:       table.Status,
		StatusName:   s.getStatusName(table.Status),
		Memo:         table.Memo,
		Bets:         bets,
		CreateTime:   table.CreateTime.Format("2006-01-02 15:04:05"),
	}
	return response
}

// getGameTypeName 获取游戏类型名称
func (s *TableService) getGameTypeName(gameType int8) string {
	switch gameType {
	case models.GameTypeBaccarat:
		return "百家乐"
	case models.GameTypeDragonTiger:
		return "龙虎斗"
	case models.GameTypeBaccaratNoComm:
		return "百家乐免佣"
	case models.GameTypeNiuniu:
		return "牛牛"
	case models.GameTypeSangong:
		return "三公"
	case models.GameTypeA89:
		return "A89"
	case models.GameTypeZhuangxianniu:
		return "庄闲牛"
	case models.GameTypeSicbo:
		return "骰宝"
	case models.GameTypeRoulette:
		return "轮盘"
	default:
		return "未知游戏"
	}
}

// getChannelName 获取通道名称
func (s *TableService) getChannelName(channel int8) string {
	switch channel {
	case 1:
		return "现场"
	case 2:
		return "电投"
	case 3:
		return "网投"
	default:
		return "未知通道"
	}
}

// getStatusName 获取状态名称
func (s *TableService) getStatusName(status int8) string {
	switch status {
	case 1:
		return "禁用"
	case 2:
		return "启用"
	default:
		return "未知状态"
	}
}

// ShuffleStartRequest 洗牌开场请求结构
type ShuffleStartRequest struct {
	AccountPeriod     string `json:"account_period" binding:"required"` // 账期
	Shift             int8   `json:"shift" binding:"required"`          // 班次:1-早班;2-晚班
	ShuffleMethod     string `json:"shuffle_method"`                    // 洗牌方式
	CardColor         string `json:"card_color"`                        // 牌色
	MonitorID         string `json:"monitor_id"`                        // 监场人员ID
	MonitorName       string `json:"monitor_name"`                      // 监场人员名称
	AdminID           string `json:"admin_id"`                          // 管理员ID
	AdminName         string `json:"admin_name"`                        // 管理员名称
	ShuffleTablePoker string `json:"shuffle_table_poker"`               // 洗牌卓牌手
	TablePoker        string `json:"table_poker"`                       // 台面洗牌牌手
	MonitorPoker      string `json:"monitor_poker"`                     // 监管洗牌手
	CutCardDealer     string `json:"cut_card_dealer"`                   // 切牌人
}

// ShuffleStartResponse 洗牌开场响应结构
type ShuffleStartResponse struct {
	ShuffleRecord *ShuffleRecordResponse `json:"shuffle_record"`
	TablesStart   *TablesStartResponse   `json:"tables_start"`
	Message       string                 `json:"message"`
}

// ShuffleRecordResponse 洗牌记录响应结构
type ShuffleRecordResponse struct {
	ID                int64  `json:"id"`
	TableID           int64  `json:"table_id"`
	AccountPeriod     string `json:"account_period"`
	ShoeNo            int    `json:"shoe_no"`
	CardNo            int    `json:"card_no"`
	Shift             int8   `json:"shift"`
	ShiftName         string `json:"shift_name"`
	ShuffleMethod     string `json:"shuffle_method"`
	CardColor         string `json:"card_color"`
	MonitorID         string `json:"monitor_id"`
	MonitorName       string `json:"monitor_name"`
	AdminID           string `json:"admin_id"`
	AdminName         string `json:"admin_name"`
	ShuffleTablePoker string `json:"shuffle_table_poker"`
	TablePoker        string `json:"table_poker"`
	MonitorPoker      string `json:"monitor_poker"`
	CutCardDealer     string `json:"cut_card_dealer"`
	CreateTime        string `json:"create_time"`
}

// TablesStartResponse 桌台账期响应结构
type TablesStartResponse struct {
	ID            int64  `json:"id"`
	TableID       int64  `json:"table_id"`
	AccountPeriod string `json:"account_period"`
	GameType      int8   `json:"game_type"`
	GameTypeName  string `json:"game_type_name"`
	Stats         int8   `json:"stats"`
	StatsName     string `json:"stats_name"`
	ShoeNo        int    `json:"shoe_no"`
	HandNo        int    `json:"hand_no"`
	CreateTime    string `json:"create_time"`
}

// StartShuffle 洗牌开场
func (s *TableService) StartShuffle(req ShuffleStartRequest, clientIP string) (*ShuffleStartResponse, error) {
	// 参数验证
	if req.AccountPeriod == "" {
		return nil, errors.New("账期不能为空")
	}
	if req.Shift < 1 || req.Shift > 2 {
		return nil, errors.New("班次无效")
	}

	// 通过IP获取桌台ID
	tableID, err := s.GetTableIDByIP(clientIP)
	if err != nil {
		return nil, err
	}

	// 验证桌台是否存在且启用
	var table models.Table
	err = database.DB.Where("id = ? AND status = ?", tableID, 2).First(&table).Error
	if err != nil {
		return nil, errors.New("桌台不存在或未启用")
	}

	// 解析账期
	accountPeriod, err := time.Parse("********", req.AccountPeriod)
	if err != nil {
		return nil, errors.New("账期格式错误，请使用 YYYYMMDD 格式")
	}

	// 开启数据库事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 获取或创建tables_start记录
	var tablesStart models.TablesStart
	err = tx.Where("table_id = ? AND account_period = ?", tableID, req.AccountPeriod).First(&tablesStart).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果记录不存在，创建新记录
			tablesStart = models.TablesStart{
				TableID:       tableID,
				AccountPeriod: req.AccountPeriod,
				GameType:      table.GameType,
				Stats:         1, // 设置状态为销售中
				ShoeNo:        1, // 场次编号从1开始
				HandNo:        1, // 局号编号从1开始
				CreateTime:    time.Now(),
			}
			err = tx.Create(&tablesStart).Error
			if err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("创建桌台账期记录失败: %v", err)
			}
		} else {
			tx.Rollback()
			return nil, fmt.Errorf("查询桌台账期记录失败: %v", err)
		}
	} else {
		// 如果记录存在，场次编号累加1，局号编号重置为1
		tablesStart.ShoeNo++
		tablesStart.HandNo = 1 // 局号从1开始
		tablesStart.Stats = 1  // 设置状态为销售中
		err = tx.Save(&tablesStart).Error
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("更新桌台账期记录失败: %v", err)
		}
	}

	// 创建洗牌记录
	shuffleRecord := models.ShuffleRecord{
		TableID:           tableID,
		AccountPeriod:     accountPeriod,
		ShoeNo:            tablesStart.ShoeNo,
		CardNo:            1, // 牌次号从1开始
		Shift:             req.Shift,
		ShuffleMethod:     req.ShuffleMethod,
		CardColor:         req.CardColor,
		MonitorID:         req.MonitorID,
		MonitorName:       req.MonitorName,
		AdminID:           req.AdminID,
		AdminName:         req.AdminName,
		ShuffleTablePoker: req.ShuffleTablePoker,
		TablePoker:        req.TablePoker,
		MonitorPoker:      req.MonitorPoker,
		CutCardDealer:     req.CutCardDealer,
		CreateTime:        time.Now(),
	}

	err = tx.Create(&shuffleRecord).Error
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建洗牌记录失败: %v", err)
	}

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	// 构造响应数据
	response := &ShuffleStartResponse{
		ShuffleRecord: s.FormatShuffleRecordResponse(&shuffleRecord),
		TablesStart:   s.FormatTablesStartResponse(&tablesStart),
		Message:       "洗牌开场成功",
	}

	return response, nil
}

// FormatShuffleRecordResponse 格式化洗牌记录响应数据
func (s *TableService) FormatShuffleRecordResponse(record *models.ShuffleRecord) *ShuffleRecordResponse {
	return &ShuffleRecordResponse{
		ID:                record.ID,
		TableID:           record.TableID,
		AccountPeriod:     record.AccountPeriod.Format("2006-01-02"),
		ShoeNo:            record.ShoeNo,
		CardNo:            record.CardNo,
		Shift:             record.Shift,
		ShiftName:         s.getShiftName(record.Shift),
		ShuffleMethod:     record.ShuffleMethod,
		CardColor:         record.CardColor,
		MonitorID:         record.MonitorID,
		MonitorName:       record.MonitorName,
		AdminID:           record.AdminID,
		AdminName:         record.AdminName,
		ShuffleTablePoker: record.ShuffleTablePoker,
		TablePoker:        record.TablePoker,
		MonitorPoker:      record.MonitorPoker,
		CutCardDealer:     record.CutCardDealer,
		CreateTime:        record.CreateTime.Format("2006-01-02 15:04:05"),
	}
}

// FormatTablesStartResponse 格式化桌台账期响应数据
func (s *TableService) FormatTablesStartResponse(record *models.TablesStart) *TablesStartResponse {
	return &TablesStartResponse{
		ID:            record.ID,
		TableID:       record.TableID,
		AccountPeriod: record.AccountPeriod,
		GameType:      record.GameType,
		GameTypeName:  s.getGameTypeName(record.GameType),
		Stats:         record.Stats,
		StatsName:     s.getTablesStartStatusName(record.Stats),
		ShoeNo:        record.ShoeNo,
		HandNo:        record.HandNo,
		CreateTime:    record.CreateTime.Format("2006-01-02 15:04:05"),
	}
}

// getShiftName 获取班次名称
func (s *TableService) getShiftName(shift int8) string {
	switch shift {
	case 1:
		return "早班"
	case 2:
		return "晚班"
	default:
		return "未知"
	}
}

// getTablesStartStatusName 获取桌台账期状态名称
func (s *TableService) getTablesStartStatusName(status int8) string {
	switch status {
	case 1:
		return "销售中"
	case 2:
		return "等待洗牌"
	case 3:
		return "等待出码"
	case 4:
		return "已收盘"
	default:
		return "未知"
	}
}

// CloseTableRequest 收盘请求结构体
type CloseTableRequest struct {
	// 桌台ID将通过IP自动获取，不再需要传递
}

// CloseTableResponse 收盘响应结构体
type CloseTableResponse struct {
	Message     string               `json:"message"`
	TablesStart *TablesStartResponse `json:"tables_start"`
}

// GetLatestTablesStartResponse 通过桌台编号查询最新开台数据响应结构体
type GetLatestTablesStartResponse struct {
	Message     string               `json:"message"`
	TablesStart *TablesStartResponse `json:"tables_start"`
}

// CloseTable 收盘功能
func (s *TableService) CloseTable(req CloseTableRequest, clientIP string) (*CloseTableResponse, error) {
	// 通过IP获取桌台ID
	tableID, err := s.GetTableIDByIP(clientIP)
	if err != nil {
		return nil, fmt.Errorf("获取桌台信息失败: %v", err)
	}

	// 验证桌台是否存在且启用
	var table models.Table
	err = database.DB.Where("id = ? AND status = ?", tableID, 2).First(&table).Error
	if err != nil {
		return nil, errors.New("桌台不存在或未启用")
	}

	// 开启数据库事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查找当前桌台最新的账期记录
	var existingRecord models.TablesStart
	err = tx.Where("table_id = ?", tableID).Order("account_period DESC").First(&existingRecord).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			tx.Rollback()
			return nil, errors.New("未找到桌台的账期记录")
		}
		tx.Rollback()
		return nil, fmt.Errorf("查询桌台账期记录失败: %v", err)
	}

	// 检查当前账期记录是否已经是已收盘状态
	if existingRecord.Stats == 4 {
		tx.Rollback()
		return nil, fmt.Errorf("桌台%d在账期%s已经是已收盘状态", tableID, existingRecord.AccountPeriod)
	}

	// 更新当前账期记录状态为已收盘
	existingRecord.Stats = 4 // 4-已收盘
	err = tx.Save(&existingRecord).Error
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("更新桌台账期记录失败: %v", err)
	}

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	// 构造响应数据
	response := &CloseTableResponse{
		Message:     "收盘成功",
		TablesStart: s.FormatTablesStartResponse(&existingRecord),
	}

	return response, nil
}

// GetLatestTablesStartByIP 通过客户端IP查询最新的开台数据
func (s *TableService) GetLatestTablesStartByIP(ip string) (*GetLatestTablesStartResponse, error) {
	// 参数验证
	if ip == "" {
		return nil, errors.New("IP地址不能为空")
	}

	// 先尝试从Redis获取桌台信息
	cacheData, err := s.GetTableInfoFromRedis(ip)
	var tableID int64
	var gameType int8

	if err != nil {
		// Redis中没有缓存，从数据库查询
		table, err := s.GetTableByIP(ip)
		if err != nil {
			return nil, fmt.Errorf("未找到IP为 %s 的启用桌台", ip)
		}
		tableID = int64(table.ID)
		gameType = table.GameType

		// 缓存到Redis
		_ = s.CacheTableInfoToRedis(ip, table)
	} else {
		// 从Redis缓存中获取桌台信息
		if id, ok := cacheData["table_id"].(float64); ok {
			tableID = int64(id)
		} else {
			return nil, errors.New("Redis缓存数据格式错误")
		}
		if gt, ok := cacheData["game_type"].(float64); ok {
			gameType = int8(gt)
		} else {
			return nil, errors.New("Redis缓存数据格式错误")
		}
	}

	// 查询该桌台最新的开台数据（按创建时间倒序）
	var tablesStart models.TablesStart
	err = database.DB.Where("table_id = ?", tableID).Order("create_time DESC").First(&tablesStart).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 没有找到开台数据，创建一条新的开台数据
			currentDate := time.Now().Format("********")

			// 创建新的开台数据
			newTablesStart := models.TablesStart{
				TableID:       tableID,
				AccountPeriod: currentDate,
				GameType:      gameType,
				Stats:         1, // 1-等待出码
				ShoeNo:        0, // 场次编号为0
				HandNo:        0, // 局号编号为0
				CreateTime:    time.Now(),
			}

			// 保存到数据库
			err = database.DB.Create(&newTablesStart).Error
			if err != nil {
				return nil, fmt.Errorf("创建新开台数据失败: %v", err)
			}

			// 构造响应数据
			response := &GetLatestTablesStartResponse{
				Message:     "未找到开台数据，已创建新的开台数据",
				TablesStart: s.FormatTablesStartResponse(&newTablesStart),
			}

			return response, nil
		}
		return nil, fmt.Errorf("查询开台数据失败: %v", err)
	}

	// 构造响应数据
	response := &GetLatestTablesStartResponse{
		Message:     "查询成功",
		TablesStart: s.FormatTablesStartResponse(&tablesStart),
	}

	return response, nil
}

// GetTableIDByIP 通过IP获取桌台ID
func (s *TableService) GetTableIDByIP(ip string) (int64, error) {
	// 参数验证
	if ip == "" {
		return 0, errors.New("IP地址不能为空")
	}

	// 先尝试从Redis获取桌台信息
	cacheData, err := s.GetTableInfoFromRedis(ip)
	if err == nil {
		// 从Redis缓存中获取桌台ID
		if id, ok := cacheData["table_id"].(float64); ok {
			return int64(id), nil
		}
	}

	// Redis中没有缓存，从数据库查询
	table, err := s.GetTableByIP(ip)
	if err != nil {
		return 0, fmt.Errorf("未找到IP为 %s 的启用桌台", ip)
	}

	// 缓存到Redis
	_ = s.CacheTableInfoToRedis(ip, table)

	return int64(table.ID), nil
}

// GetHandRecordsResultRequest 查询条口记录结果请求结构
type GetHandRecordsResultRequest struct {
	AccountPeriod string `json:"account_period" binding:"required"` // 账期
}

// GetHandRecordsResultResponse 查询条口记录结果响应结构
type GetHandRecordsResultResponse struct {
	Message    string              `json:"message"`
	TableID    int64               `json:"table_id"`
	TableCode  string              `json:"table_code"`
	Records    []*HandRecordResult `json:"records"`
	TotalCount int                 `json:"total_count"`
}

// HandRecordResult 条口记录结果结构
type HandRecordResult struct {
	ID            int64  `json:"id"`
	TableID       int64  `json:"table_id"`
	AccountPeriod string `json:"account_period"`
	ShoeNo        int    `json:"shoe_no"`
	HandNo        int    `json:"hand_no"`
	Result1       string `json:"result_1"`
	CreateTime    string `json:"create_time"`
}

// GetHandRecordsResult 获取条口记录结果（用于露珠图）
func (s *TableService) GetHandRecordsResult(req GetHandRecordsResultRequest, clientIP string) (*GetHandRecordsResultResponse, error) {
	// 参数验证
	if req.AccountPeriod == "" {
		return nil, errors.New("账期不能为空")
	}

	// 验证账期格式
	if _, err := time.Parse("********", req.AccountPeriod); err != nil {
		return nil, errors.New("账期格式错误，请使用 YYYYMMDD 格式")
	}

	// 通过IP获取桌台信息
	table, err := s.GetTableByIP(clientIP)
	if err != nil {
		return nil, fmt.Errorf("获取桌台信息失败: %v", err)
	}

	// 查询该桌台该账期的所有条口记录，按场次和局数排序
	var handRecords []models.HandRecord
	err = database.DB.Where("table_id = ? AND account_period = ?", table.ID, req.AccountPeriod).
		Order("shoe_no ASC, hand_no ASC").
		Find(&handRecords).Error
	if err != nil {
		return nil, fmt.Errorf("查询条口记录失败: %v", err)
	}

	// 格式化响应数据
	records := make([]*HandRecordResult, len(handRecords))
	for i, record := range handRecords {
		records[i] = &HandRecordResult{
			ID:            record.ID,
			TableID:       record.TableID,
			AccountPeriod: record.AccountPeriod,
			ShoeNo:        record.ShoeNo,
			HandNo:        record.HandNo,
			Result1:       record.Result1,
			CreateTime:    record.CreateTime.Format("2006-01-02 15:04:05"),
		}
	}

	// 构造响应
	response := &GetHandRecordsResultResponse{
		Message:    "查询成功",
		TableID:    int64(table.ID),
		TableCode:  table.TableCode,
		Records:    records,
		TotalCount: len(records),
	}

	return response, nil
}

// GetTableStatsRequest 获取桌台统计请求
type GetTableStatsRequest struct {
	AccountPeriod string `json:"account_period" binding:"required"` // 账期
}

// GetTableStatsResponse 获取桌台统计响应
type GetTableStatsResponse struct {
	Message       string                   `json:"message"`
	TableID       int64                    `json:"table_id"`
	TableCode     string                   `json:"table_code"`
	AccountPeriod string                   `json:"account_period"`
	ShuffleInfo   *LatestShuffleInfo       `json:"shuffle_info"`  // 最新洗牌信息
	FinanceStats  map[string]*FinanceStats `json:"finance_stats"` // 财务统计，按货币类型分组
	HandStats     map[string]*HandStats    `json:"hand_stats"`    // 手牌统计，按货币类型分组
}

// LatestShuffleInfo 最新洗牌信息
type LatestShuffleInfo struct {
	ShuffleMethod string `json:"shuffle_method"`  // 洗牌方式
	TablePoker    string `json:"table_poker"`     // 台面洗牌牌手
	CutCardDealer string `json:"cut_card_dealer"` // 切牌人
	ShoeNo        int    `json:"shoe_no"`         // 场次编号
	CardNo        int    `json:"card_no"`         // 牌次号
	CreateTime    string `json:"create_time"`     // 创建时间
}

// FinanceStats 财务统计
type FinanceStats struct {
	CurrencyType     int8    `json:"currency_type"`      // 货币类型
	CurrencyTypeName string  `json:"currency_type_name"` // 货币类型名称
	OutCodeAmount    float64 `json:"out_code_amount"`    // 出码量
	AddCodeAmount    float64 `json:"add_code_amount"`    // 加彩量
}

// HandStats 手牌统计（按货币类型分组）
type HandStats struct {
	CurrencyType     int8    `json:"currency_type"`      // 货币类型
	CurrencyTypeName string  `json:"currency_type_name"` // 货币类型名称
	TotalWashAmount  float64 `json:"total_wash_amount"`  // 本期洗码量合计
	TotalTipAmount   float64 `json:"total_tip_amount"`   // 小费合计
	TotalWinLoss     float64 `json:"total_win_loss"`     // 输赢金额合计
	LatestWinLoss    float64 `json:"latest_win_loss"`    // 最新一局的输赢金额
	TotalCodeAmount  float64 `json:"total_code_amount"`  // 总码量(出码量+加彩-输赢金额合计)
}

// GetTableStats 获取桌台情况统计
func (s *TableService) GetTableStats(req GetTableStatsRequest, clientIP string) (*GetTableStatsResponse, error) {
	// 获取桌台信息
	table, err := s.GetTableByIP(clientIP)
	if err != nil {
		return nil, fmt.Errorf("获取桌台信息失败: %v", err)
	}

	// 解析账期
	accountPeriod, err := time.Parse("2006-01-02", req.AccountPeriod)
	if err != nil {
		return nil, fmt.Errorf("账期格式错误，请使用 YYYY-MM-DD 格式: %v", err)
	}

	// 获取最新洗牌信息
	shuffleInfo, err := s.getLatestShuffleInfo(int64(table.ID), accountPeriod)
	if err != nil {
		return nil, fmt.Errorf("获取洗牌信息失败: %v", err)
	}

	// 获取财务统计
	financeStats, err := s.getFinanceStats(int64(table.ID), accountPeriod)
	if err != nil {
		return nil, fmt.Errorf("获取财务统计失败: %v", err)
	}

	// 获取手牌统计
	handStats, err := s.getHandStats(int64(table.ID), req.AccountPeriod)
	if err != nil {
		return nil, fmt.Errorf("获取手牌统计失败: %v", err)
	}

	// 为每种货币类型计算总码量
	for currencyKey, handStat := range handStats {
		var outCodeAmount, addCodeAmount float64
		if financeStat, exists := financeStats[currencyKey]; exists {
			outCodeAmount = financeStat.OutCodeAmount
			addCodeAmount = financeStat.AddCodeAmount
		}
		handStat.TotalCodeAmount = outCodeAmount + addCodeAmount - handStat.TotalWinLoss
	}

	response := &GetTableStatsResponse{
		Message:       "获取桌台统计成功",
		TableID:       int64(table.ID),
		TableCode:     table.TableCode,
		AccountPeriod: req.AccountPeriod,
		ShuffleInfo:   shuffleInfo,
		FinanceStats:  financeStats,
		HandStats:     handStats,
	}

	return response, nil
}

// getLatestShuffleInfo 获取最新洗牌信息
func (s *TableService) getLatestShuffleInfo(tableID int64, accountPeriod time.Time) (*LatestShuffleInfo, error) {
	var shuffleRecord models.ShuffleRecord
	err := database.DB.Where("table_id = ? AND account_period = ?", tableID, accountPeriod).
		Order("create_time DESC").
		First(&shuffleRecord).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &LatestShuffleInfo{}, nil // 返回空信息而不是错误
		}
		return nil, err
	}

	return &LatestShuffleInfo{
		ShuffleMethod: shuffleRecord.ShuffleMethod,
		TablePoker:    shuffleRecord.TablePoker,
		CutCardDealer: shuffleRecord.CutCardDealer,
		ShoeNo:        shuffleRecord.ShoeNo,
		CardNo:        shuffleRecord.CardNo,
		CreateTime:    shuffleRecord.CreateTime.Format("2006-01-02 15:04:05"),
	}, nil
}

// getFinanceStats 获取财务统计
func (s *TableService) getFinanceStats(tableID int64, accountPeriod time.Time) (map[string]*FinanceStats, error) {
	var records []models.FinanceRecord

	// 查询当前帐期审核通过的记录
	err := database.DB.Where("table_id = ? AND account_period = ? AND status = ?",
		tableID, accountPeriod, 2). // status = 2 表示审核通过
		Find(&records).Error

	if err != nil {
		return nil, err
	}

	// 按货币类型分组统计
	financeStats := make(map[string]*FinanceStats)

	for _, record := range records {
		currencyKey := s.getCurrencyTypeName(record.CurrencyType)

		if financeStats[currencyKey] == nil {
			financeStats[currencyKey] = &FinanceStats{
				CurrencyType:     record.CurrencyType,
				CurrencyTypeName: currencyKey,
				OutCodeAmount:    0,
				AddCodeAmount:    0,
			}
		}

		switch record.Type {
		case 1: // 出码
			financeStats[currencyKey].OutCodeAmount += record.TotalAmount
		case 3: // 加彩
			financeStats[currencyKey].AddCodeAmount += record.TotalAmount
		}
	}

	return financeStats, nil
}

// getHandStats 获取手牌统计
func (s *TableService) getHandStats(tableID int64, accountPeriod string) (map[string]*HandStats, error) {
	var records []models.HandRecord

	// 查询当前帐期的手牌记录
	err := database.DB.Where("table_id = ? AND account_period = ?", tableID, accountPeriod).
		Find(&records).Error

	if err != nil {
		return nil, err
	}

	// 按货币类型分组统计
	handStats := make(map[string]*HandStats)
	latestTimeMap := make(map[int8]time.Time) // 每种货币类型的最新时间

	for _, record := range records {
		currencyKey := s.getCurrencyTypeName(record.CurrencyType)

		if handStats[currencyKey] == nil {
			handStats[currencyKey] = &HandStats{
				CurrencyType:     record.CurrencyType,
				CurrencyTypeName: currencyKey,
				TotalWashAmount:  0,
				TotalTipAmount:   0,
				TotalWinLoss:     0,
				LatestWinLoss:    0,
				TotalCodeAmount:  0,
			}
		}

		handStats[currencyKey].TotalWashAmount += record.WashAmount
		handStats[currencyKey].TotalTipAmount += record.AmountTip
		handStats[currencyKey].TotalWinLoss += record.WinLoss

		// 找到每种货币类型最新一局的输赢金额
		if record.CreateTime.After(latestTimeMap[record.CurrencyType]) {
			latestTimeMap[record.CurrencyType] = record.CreateTime
			handStats[currencyKey].LatestWinLoss = record.WinLoss
		}
	}

	return handStats, nil
}

// getCurrencyTypeName 获取货币类型名称
func (s *TableService) getCurrencyTypeName(currencyType int8) string {
	switch currencyType {
	case 1:
		return "筹码"
	case 2:
		return "现金"
	case 3:
		return "U码"
	default:
		return "未知"
	}
}
