package services

import (
	"accounting_enter/internal/database"
	"accounting_enter/internal/models"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"gorm.io/gorm"
)

// BetService 下注服务
type BetService struct{}

// NewBetService 创建下注服务实例
func NewBetService() *BetService {
	return &BetService{}
}

// BetRecordRequest 下注记录请求结构
type BetRecordRequest struct {
	TableID          int64   `json:"table_id" binding:"required"`       // 桌台ID (在批量下注时通过IP获取)
	GameType         int8    `json:"game_type" binding:"required"`      // 游戏类型:1-百家乐;2-龙虎斗;3-百家乐免佣;4-牛牛;5-三公;6-A89;7-庄闲牛;8-骰宝;-轮盘;
	AccountPeriod    string  `json:"account_period" binding:"required"` // 账期
	RoundNo          int     `json:"round_no" binding:"required"`       // 场次编号
	HandNo           int     `json:"hand_no" binding:"required"`        // 局号编号
	WashCode         string  `json:"wash_code" binding:"required"`      // 洗码号
	UserName         string  `json:"user_name"`                         // 客户姓名
	CurrencyType     int8    `json:"currency_type" binding:"required"`  // 货币类型:1-筹码;2-现金;3-U码
	BankerAmount     float64 `json:"banker_amount"`                     // 庄-龙
	PlayerAmount     float64 `json:"player_amount"`                     // 闲-虎
	TieAmount        float64 `json:"tie_amount"`                        // 和
	BankerPairAmount float64 `json:"banker_pair_amount"`                // 庄对
	PlayerPairAmount float64 `json:"player_pair_amount"`                // 闲对
	Lucky6Amount     float64 `json:"lucky_6_amount"`                    // 幸运6
	Lucky7Amount     float64 `json:"lucky_7_amount"`                    // 幸运7
	WinResult        string  `json:"win_result"`                        // 结果
	WinLoss          float64 `json:"win_loss"`                          // 输赢金额
	Loss             float64 `json:"loss"`                              // 输口
	AmountTip        float64 `json:"amount_tip"`                        // 小费金额
	AmountBottom     float64 `json:"amount_bottom"`                     // 和底
	WashRate         float64 `json:"wash_rate"`                         // 洗码率
	WashAmount       float64 `json:"wash_amount"`                       // 本局洗码量
	WashTip          float64 `json:"wash_tip"`                          // 洗码费
}

// BetRecordResponse 下注记录响应结构
type BetRecordResponse struct {
	ID               int64   `json:"id"`
	TableID          int64   `json:"table_id"`
	AccountPeriod    string  `json:"account_period"`
	RoundNo          int     `json:"round_no"`
	HandNo           int     `json:"hand_no"`
	WashCode         string  `json:"wash_code"`
	UserName         string  `json:"user_name"`
	CurrencyType     int8    `json:"currency_type"`
	CurrencyTypeName string  `json:"currency_type_name"`
	BankerAmount     float64 `json:"banker_amount"`
	PlayerAmount     float64 `json:"player_amount"`
	TieAmount        float64 `json:"tie_amount"`
	BankerPairAmount float64 `json:"banker_pair_amount"`
	PlayerPairAmount float64 `json:"player_pair_amount"`
	Lucky6Amount     float64 `json:"lucky_6_amount"`
	Lucky7Amount     float64 `json:"lucky_7_amount"`
	TotalBetAmount   float64 `json:"total_bet_amount"`
	WinResult        string  `json:"win_result"`
	WinLoss          float64 `json:"win_loss"`
	Loss             float64 `json:"loss"`
	AmountTip        float64 `json:"amount_tip"`
	AmountBottom     float64 `json:"amount_bottom"`
	WashRate         float64 `json:"wash_rate"`
	WashAmount       float64 `json:"wash_amount"`
	WashTip          float64 `json:"wash_tip"`
	CreateTime       string  `json:"create_time"`
}

// EnterResultRequest 结果录入请求结构体
// 用于百家乐结果录入
// 入参：账期、场次编号、局号编号、结果（可多项组合）
type EnterResultRequest struct {
	AccountPeriod string   `json:"account_period" binding:"required"` // 账期
	RoundNo       int      `json:"round_no" binding:"required"`       // 场次编号
	HandNo        int      `json:"hand_no" binding:"required"`        // 局号编号
	Result        []string `json:"result" binding:"required"`         // 结果项（如["庄","庄对","闲对"]）
}

// EnterResult 录入结果并结算
func (s *BetService) EnterResult(req EnterResultRequest, clientIP string) (map[string]interface{}, error) {
	// 参数验证
	if clientIP == "" {
		return nil, errors.New("客户端IP不能为空")
	}

	// 通过IP获取桌台ID
	tableService := NewTableService()
	tableID, err := tableService.GetTableIDByIP(clientIP)
	if err != nil {
		return nil, fmt.Errorf("获取桌台信息失败: %v", err)
	}
	if req.AccountPeriod == "" {
		return nil, errors.New("账期不能为空")
	}
	if req.RoundNo <= 0 {
		return nil, errors.New("场次编号不能为空")
	}
	if req.HandNo <= 0 {
		return nil, errors.New("局号编号不能为空")
	}
	if len(req.Result) == 0 {
		return nil, errors.New("结果不能为空")
	}

	// 验证账期格式
	if _, err := time.Parse("********", req.AccountPeriod); err != nil {
		return nil, errors.New("账期格式错误，请使用 YYYYMMDD 格式")
	}

	// 验证桌台是否存在且启用
	var table models.Table
	err = database.DB.Where("id = ? AND status = ?", tableID, 2).First(&table).Error
	if err != nil {
		return nil, errors.New("桌台不存在或未启用")
	}

	// 从Redis获取该局的所有下注记录
	ctx := context.Background()

	// 获取该局的所有单条下注记录
	redisPattern := fmt.Sprintf("%d:%s:%d:%d:*", tableID, req.AccountPeriod, req.RoundNo, req.HandNo)
	keys, err := database.RedisClient.Keys(ctx, redisPattern).Result()
	if err != nil {
		return nil, fmt.Errorf("获取Redis下注记录失败: %v", err)
	}

	if len(keys) == 0 {
		return nil, errors.New("该局没有找到下注记录")
	}

	var betRecords []models.BetRecord
	var totalByCurrency = make(map[string]map[string]float64)

	// 开启数据库事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 获取所有下注记录
	for _, key := range keys {
		// 从Redis获取下注记录
		betRecordJSON, err := database.RedisClient.Get(ctx, key).Result()
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("获取Redis记录失败: %v", err)
		}

		// 反序列化
		var betReq BetRecordRequest
		err = json.Unmarshal([]byte(betRecordJSON), &betReq)
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("反序列化下注记录失败: %v", err)
		}

		// 检查数据库中是否已存在该记录（避免重复结算）
		var existingRecord models.BetRecord
		err = tx.Where(
			"wash_code = ? AND table_id = ? AND account_period = ? AND hand_no = ? AND currency_type = ?",
			betReq.WashCode, betReq.TableID, betReq.AccountPeriod, betReq.HandNo, betReq.CurrencyType,
		).First(&existingRecord).Error

		if err == nil {
			// 记录已存在，跳过
			continue
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			tx.Rollback()
			return nil, fmt.Errorf("检查记录唯一性失败: %v", err)
		}

		// 计算结算结果
		winLoss, loss, washAmount, washTip, amountTip := s.calculateBaccaratResult(betReq, req.Result, table.WashRate)

		// 创建下注记录
		betRecord := models.BetRecord{
			TableID:          betReq.TableID,
			GameType:         betReq.GameType,
			AccountPeriod:    betReq.AccountPeriod,
			RoundNo:          betReq.RoundNo,
			HandNo:           betReq.HandNo,
			WashCode:         betReq.WashCode,
			UserName:         betReq.UserName,
			CurrencyType:     betReq.CurrencyType,
			BankerAmount:     betReq.BankerAmount,
			PlayerAmount:     betReq.PlayerAmount,
			TieAmount:        betReq.TieAmount,
			BankerPairAmount: betReq.BankerPairAmount,
			PlayerPairAmount: betReq.PlayerPairAmount,
			Lucky6Amount:     betReq.Lucky6Amount,
			Lucky7Amount:     betReq.Lucky7Amount,
			WinResult:        strings.Join(req.Result, ","),
			WinLoss:          winLoss,
			Loss:             loss,
			AmountTip:        amountTip,
			AmountBottom:     betReq.AmountBottom,
			WashRate:         table.WashRate,
			WashAmount:       washAmount,
			WashTip:          washTip,
			CreateTime:       time.Now(),
		}

		// 保存到数据库
		err = tx.Create(&betRecord).Error
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("保存下注记录失败: %v", err)
		}

		betRecords = append(betRecords, betRecord)

		// 统计各货币类型的总输赢
		currencyName := s.getCurrencyTypeName(betRecord.CurrencyType)
		if totalByCurrency[currencyName] == nil {
			totalByCurrency[currencyName] = make(map[string]float64)
		}
		totalByCurrency[currencyName]["win_loss"] += betRecord.WinLoss
		totalByCurrency[currencyName]["loss"] += betRecord.Loss

		// 从Redis删除已处理的记录
		err = database.RedisClient.Del(ctx, key).Err()
		if err != nil {
			// 删除失败不影响主流程，只记录错误
			fmt.Printf("删除Redis记录失败: %v\n", err)
		}
	}

	// 根据货币类型统计并创建hand_records记录
	currencyStats := make(map[int8]map[string]float64)
	for _, betRecord := range betRecords {
		currencyType := betRecord.CurrencyType
		if currencyStats[currencyType] == nil {
			currencyStats[currencyType] = make(map[string]float64)
		}

		// 计算下注金额总和
		totalBetAmount := betRecord.BankerAmount + betRecord.PlayerAmount + betRecord.TieAmount +
			betRecord.BankerPairAmount + betRecord.PlayerPairAmount + betRecord.Lucky6Amount + betRecord.Lucky7Amount

		currencyStats[currencyType]["bet_amount"] += totalBetAmount
		currencyStats[currencyType]["win_loss"] += betRecord.WinLoss
		currencyStats[currencyType]["loss"] += betRecord.Loss
		currencyStats[currencyType]["amount_tip"] += betRecord.AmountTip
		currencyStats[currencyType]["amount_bottom"] += betRecord.AmountBottom
		currencyStats[currencyType]["wash_amount"] += betRecord.WashAmount
		currencyStats[currencyType]["wash_tip"] += betRecord.WashTip
	}

	// 为每种货币类型创建hand_records记录
	for currencyType, stats := range currencyStats {
		handRecord := models.HandRecord{
			TableID:       tableID,
			AccountPeriod: req.AccountPeriod,
			ShoeNo:        req.RoundNo,
			HandNo:        req.HandNo,
			GameType:      1, // 百家乐游戏类型
			CurrencyType:  currencyType,
			BetAmount:     stats["bet_amount"],
			Result1:       strings.Join(req.Result, ","),
			WinLoss:       stats["win_loss"],
			Loss:          stats["loss"],
			AmountTip:     stats["amount_tip"],
			AmountBottom:  stats["amount_bottom"],
			WashRate:      table.WashRate,
			WashAmount:    stats["wash_amount"],
			WashTip:       stats["wash_tip"],
			CreateTime:    time.Now(),
		}

		err = tx.Create(&handRecord).Error
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("保存手牌记录失败: %v", err)
		}
	}

	// tables_start 局号+1
	tx.Model(&models.TablesStart{}).Where("table_id = ? AND account_period = ? AND shoe_no = ?",
		tableID, req.AccountPeriod, req.RoundNo).UpdateColumn("hand_no", gorm.Expr("hand_no + 1"))

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	return map[string]interface{}{
		"message":         "结算成功",
		"processed_count": len(betRecords),
		"hand_summary":    totalByCurrency,
	}, nil

}

// validateBetRecord 验证单条下注记录
func (s *BetService) validateBetRecord(req BetRecordRequest) error {
	// 基本字段验证
	if req.TableID <= 0 {
		return errors.New("桌台ID不能为空")
	}
	if req.AccountPeriod == "" {
		return errors.New("账期不能为空")
	}
	if req.RoundNo <= 0 {
		return errors.New("场次编号不能为空")
	}
	if req.HandNo <= 0 {
		return errors.New("局号编号不能为空")
	}
	if req.WashCode == "" {
		return errors.New("洗码号不能为空")
	}
	if req.CurrencyType < 1 || req.CurrencyType > 3 {
		return errors.New("货币类型无效，应为1-筹码、2-现金、3-U码")
	}

	// 验证账期格式
	if _, err := time.Parse("********", req.AccountPeriod); err != nil {
		return errors.New("账期格式错误，请使用 YYYYMMDD 格式")
	}

	// 验证桌台是否存在且启用
	var table models.Table
	err := database.DB.Where("id = ? AND status = ?", req.TableID, 2).First(&table).Error
	if err != nil {
		return errors.New("桌台不存在或未启用")
	}

	// 验证下注金额（至少要有一个大于0的下注）
	totalBetAmount := req.BankerAmount + req.PlayerAmount + req.TieAmount +
		req.BankerPairAmount + req.PlayerPairAmount + req.Lucky6Amount + req.Lucky7Amount
	if totalBetAmount <= 0 {
		return errors.New("至少需要一个下注金额大于0")
	}

	return nil
}

// FormatBetRecordResponse 格式化下注记录响应数据
func (s *BetService) FormatBetRecordResponse(record *models.BetRecord) *BetRecordResponse {
	totalBetAmount := record.BankerAmount + record.PlayerAmount + record.TieAmount +
		record.BankerPairAmount + record.PlayerPairAmount + record.Lucky6Amount + record.Lucky7Amount

	return &BetRecordResponse{
		ID:               record.ID,
		TableID:          record.TableID,
		AccountPeriod:    record.AccountPeriod,
		RoundNo:          record.RoundNo,
		HandNo:           record.HandNo,
		WashCode:         record.WashCode,
		UserName:         record.UserName,
		CurrencyType:     record.CurrencyType,
		CurrencyTypeName: s.getCurrencyTypeName(record.CurrencyType),
		BankerAmount:     record.BankerAmount,
		PlayerAmount:     record.PlayerAmount,
		TieAmount:        record.TieAmount,
		BankerPairAmount: record.BankerPairAmount,
		PlayerPairAmount: record.PlayerPairAmount,
		Lucky6Amount:     record.Lucky6Amount,
		Lucky7Amount:     record.Lucky7Amount,
		TotalBetAmount:   totalBetAmount,
		WinResult:        record.WinResult,
		WinLoss:          record.WinLoss,
		Loss:             record.Loss,
		AmountTip:        record.AmountTip,
		AmountBottom:     record.AmountBottom,
		WashRate:         record.WashRate,
		WashAmount:       record.WashAmount,
		WashTip:          record.WashTip,
		CreateTime:       record.CreateTime.Format("2006-01-02 15:04:05"),
	}
}

// getCurrencyTypeName 获取货币类型名称
func (s *BetService) getCurrencyTypeName(currencyType int8) string {
	switch currencyType {
	case 1:
		return "筹码"
	case 2:
		return "现金"
	case 3:
		return "U码"
	default:
		return "未知"
	}
}

// calculateBaccaratResult 计算百家乐结果
func (s *BetService) calculateBaccaratResult(betReq BetRecordRequest, result []string, washRate float64) (float64, float64, float64, float64, float64) {
	var winLoss float64 = 0
	var loss float64 = 0
	var washAmount float64 = 0
	var washTip float64 = 0
	var amountTip float64 = 0

	// 根据结果计算输赢和小费
	for _, res := range result {
		switch res {
		case "庄":
			winLoss += betReq.BankerAmount * 0.95 // 庄赢扣除5%佣金
			winLoss -= betReq.PlayerAmount        // 闲输
			winLoss -= betReq.TieAmount           // 和输
		case "闲":
			winLoss += betReq.PlayerAmount // 闲赢
			winLoss -= betReq.BankerAmount // 庄输
			winLoss -= betReq.TieAmount    // 和输
		case "和":
			winLoss += betReq.TieAmount * 8 // 和的赔率是1:8
			// 庄闲不输不赢
			// 和开出时，底注算小费
			amountTip += betReq.BankerAmount + betReq.PlayerAmount
		case "庄对":
			winLoss += betReq.BankerPairAmount * 11 // 庄对赔率1:11
			// 庄对开出时，底注算小费
			amountTip += betReq.BankerAmount + betReq.PlayerAmount + betReq.TieAmount
		case "闲对":
			winLoss += betReq.PlayerPairAmount * 11 // 闲对赔率1:11
			// 闲对开出时，底注算小费
			amountTip += betReq.BankerAmount + betReq.PlayerAmount + betReq.TieAmount
		case "幸运6":
			winLoss += betReq.Lucky6Amount * 12 // 幸运6赔率1:12
		case "幸运7":
			winLoss += betReq.Lucky7Amount * 40 // 幸运7赔率1:40
		}
	}

	// 计算输口（客户输口）
	loss = -winLoss
	if loss < 0 {
		loss = 0 // 如果客户赢了，输口为0
	}

	// 计算洗码量（洗码量=客户输口）
	washAmount = loss

	// 计算洗码费
	washTip = washAmount * washRate

	return winLoss, loss, washAmount, washTip, amountTip
}

// 新增：单条下注记录保存请求
type SingleBetRequest struct {
	BetRecord BetRecordRequest `json:"bet_record" binding:"required"`
}

// 新增：单条下注记录保存响应
type SingleBetResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	TableID int64  `json:"table_id"`
}

// CreateSingleBetRecord 保存单条下注记录到Redis
func (s *BetService) CreateSingleBetRecord(req SingleBetRequest, clientIP string) (*SingleBetResponse, error) {
	startTime := time.Now()
	betRecord := req.BetRecord

	// 通过IP获取桌台ID
	tableService := NewTableService()
	tableID, err := tableService.GetTableIDByIP(clientIP)
	if err != nil {
		return nil, fmt.Errorf("获取桌台信息失败: %v", err)
	}

	log.Printf("开始保存单条下注记录，桌台ID: %d, 账期: %s, 场次: %d, 局数: %d, 洗码号: %s",
		tableID, betRecord.AccountPeriod, betRecord.RoundNo, betRecord.HandNo, betRecord.WashCode)

	// 设置桌台ID（通过IP获取）
	betRecord.TableID = tableID

	// 验证下注记录
	if err := s.validateBetRecord(betRecord); err != nil {
		return nil, fmt.Errorf("下注记录验证失败: %v", err)
	}

	// 验证桌台是否存在且启用
	var table models.Table
	err = database.DB.Where("id = ? AND status = ?", tableID, 2).First(&table).Error
	if err != nil {
		return nil, errors.New("桌台不存在或未启用")
	}

	// 验证账期格式
	if _, err := time.Parse("********", betRecord.AccountPeriod); err != nil {
		return nil, errors.New("账期格式错误，请使用 YYYYMMDD 格式")
	}

	ctx := context.Background()

	// 检查Redis连接状态
	poolStats := database.RedisClient.PoolStats()
	log.Printf("Redis连接池状态: 总连接数=%d, 空闲连接数=%d, 使用中连接数=%d",
		poolStats.TotalConns, poolStats.IdleConns, poolStats.StaleConns)

	// 生成Redis键名：{table_id}:{account_period}:{round_no}:{hand_no}:{wash_code}
	redisKey := fmt.Sprintf("%d:%s:%d:%d:%s",
		tableID, betRecord.AccountPeriod, betRecord.RoundNo, betRecord.HandNo, betRecord.WashCode)

	// 序列化为JSON
	betRecordJSON, err := json.Marshal(betRecord)
	if err != nil {
		return nil, fmt.Errorf("序列化下注记录失败: %v", err)
	}

	// 保存到Redis，设置过期时间为24小时（覆盖操作）
	redisStartTime := time.Now()
	err = database.RedisClient.Set(ctx, redisKey, betRecordJSON, 24*time.Hour).Err()
	if err != nil {
		log.Printf("Redis保存失败: %v", err)
		return nil, fmt.Errorf("保存到Redis失败: %v", err)
	}
	log.Printf("Redis保存成功，耗时: %v", time.Since(redisStartTime))

	// 构造响应数据
	response := &SingleBetResponse{
		Success: true,
		Message: fmt.Sprintf("单条下注记录保存成功，洗码号: %s", betRecord.WashCode),
		TableID: tableID,
	}

	log.Printf("单条下注记录保存完成，总耗时: %v", time.Since(startTime))
	return response, nil
}
