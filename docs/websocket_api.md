# WebSocket API 文档

## 概述

本系统使用WebSocket进行实时通信，所有的前后端交互都通过WebSocket协议进行。

**服务地址**: `ws://localhost:8080/ws`
**文档版本**: v2.0
**更新时间**: 2024-12-19

## 连接方式

WebSocket连接地址：`ws://localhost:8080/ws`

## 消息格式

所有WebSocket消息都使用JSON格式，基本结构如下：

```json
{
  "type": "消息类型",
  "from": "发送者ID",
  "to": "接收者ID（可选）",
  "data": "消息数据",
  "timestamp": **********
}
```

### 响应类型规则
- **成功响应**: `{消息类型}_success`
- **错误响应**: `{消息类型}_error`
- **特殊响应**: `connection`, `pong`, `clients_list`, `broadcast`

## 接口分类

### 📊 接口统计
- **总接口数**: 22个
- **需要认证**: 14个
- **无需认证**: 8个
- **IP自动识别**: 10个

## 接口列表

### 用户认证接口

#### 1. 用户登录

**消息类型：** `login`

**说明：** 用户账号密码登录

**请求消息：**
```json
{
  "type": "login",
  "data": {
    "username": "admin",
    "password": "123456"
  }
}
```

**成功响应：**
```json
{
  "type": "login_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user_info": {
      "id": 1,
      "username": "admin",
      "realname": "管理员",
      "nickname": "Admin",
      "gender": 1,
      "gender_name": "男",
      "avatar": "",
      "mobile": "***********",
      "email": "<EMAIL>",
      "dept_id": 1,
      "status": 1,
      "status_name": "正常",
      "login_time": **********,
      "login_ip": "*************",
      "create_time": **********
    },
    "message": "登录成功"
  },
  "timestamp": **********
}
```

**错误响应：**
```json
{
  "type": "login_error",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "error": "用户名或密码错误"
  },
  "timestamp": **********
}
```

#### 2. 用户登出

**消息类型：** `logout`

**说明：** 用户登出，清除认证状态

**请求消息：**
```json
{
  "type": "logout"
}
```

**成功响应：**
```json
{
  "type": "logout_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "message": "登出成功"
  },
  "timestamp": **********
}
```

#### 3. 获取用户信息

**消息类型：** `get_user_info`

**说明：** 获取当前登录用户的详细信息

**请求消息：**
```json
{
  "type": "get_user_info"
}
```

**成功响应：**
```json
{
  "type": "user_info_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "user_info": {
      "id": 1,
      "username": "admin",
      "realname": "管理员",
      "nickname": "Admin",
      "gender": 1,
      "gender_name": "男",
      "avatar": "",
      "mobile": "***********",
      "email": "<EMAIL>",
      "dept_id": 1,
      "status": 1,
      "status_name": "正常",
      "login_time": **********,
      "login_ip": "*************",
      "create_time": **********
    }
  },
  "timestamp": **********
}
```

**错误响应：**
```json
{
  "type": "user_info_error",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "error": "用户未登录"
  },
  "timestamp": **********
}
```

#### 4. 修改密码

**消息类型：** `change_password`

**说明：** 修改当前用户密码

**请求消息：**
```json
{
  "type": "change_password",
  "data": {
    "old_password": "123456",
    "new_password": "newpassword123"
  }
}
```

**成功响应：**
```json
{
  "type": "change_password_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "message": "密码修改成功"
  },
  "timestamp": **********
}
```

**错误响应：**
```json
{
  "type": "change_password_error",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "error": "原密码错误"
  },
  "timestamp": **********
}
```

#### 5. 验证令牌

**消息类型：** `verify_token`

**说明：** 验证JWT令牌的有效性

**请求消息：**
```json
{
  "type": "verify_token",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

**成功响应：**
```json
{
  "type": "verify_token_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "valid": true,
    "user_id": 1,
    "username": "admin",
    "realname": "管理员",
    "expires": **********
  },
  "timestamp": **********
}
```

**错误响应：**
```json
{
  "type": "verify_token_error",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "error": "令牌解析失败",
    "valid": false
  },
  "timestamp": **********
}
```



### 桌台信息接口

#### 1. 获取桌台信息

**消息类型：** `get_table_info`

**说明：** 根据客户端IP地址自动获取对应的桌台信息

**请求消息：**
```json
{
  "type": "get_table_info"
}
```

**成功响应：**
```json
{
  "type": "table_info_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "table": {
      "id": 1,
      "table_code": "T001",
      "table_name": "百家乐桌台1",
      "game_type": 1,
      "game_type_name": "百家乐",
      "table_ip": "*************",
      "video_url": "rtmp://xxx",
      "channel": 1,
      "channel_name": "现场",
      "wash_user_type": "1,2",
      "wash_rate": 0.0180,
      "max_bet_u": 1000000.00,
      "max_bet_cash": 500000.00,
      "max_bet_chips": 2000000.00,
      "tie_rate": 0.0800,
      "status": 2,
      "status_name": "启用",
      "memo": "主要桌台",
      "bets": [
        {
          "id": 1,
          "table_id": 1,
          "bet_area": "庄",
          "odds": 1.9500,
          "max_bet_u": 100000.00,
          "max_bet_cash": 50000.00,
          "max_bet_chips": 200000.00,
          "create_time": "2024-01-01 10:00:00"
        }
      ],
      "create_time": "2024-01-01 10:00:00"
    },
    "client_ip": "*************"
  },
  "timestamp": **********
}
```

**错误响应：**
```json
{
  "type": "table_info_error",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "error": "未找到IP为 ************* 的启用桌台",
    "client_ip": "*************"
  },
  "timestamp": **********
}
```

#### 2. 通过客户端IP查询最新开台数据（推荐）

**消息类型：** `get_latest_tables_start`

**说明：** 通过客户端IP自动获取对应桌台的最新开台数据，无需传参，系统会自动从Redis缓存或数据库获取桌台信息

**请求消息：**
```json
{
  "type": "get_latest_tables_start"
}
```

**成功响应：**
```json
{
  "type": "get_latest_tables_start_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "message": "查询成功",
    "tables_start": {
      "id": 1,
      "table_id": 1,
      "account_period": "********",
      "game_type": 1,
      "game_type_name": "百家乐",
      "stats": 1,
      "stats_name": "销售中",
      "shoe_no": 3,
      "hand_no": 15,
      "create_time": "2025-01-08 10:30:00"
    },
    "client_ip": "************"
  },
  "timestamp": **********
}
```

**错误响应：**
```json
{
  "type": "get_latest_tables_start_error",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "error": "未找到IP为 ************ 的启用桌台",
    "client_ip": "************"
  },
  "timestamp": **********
}
```

#### 3. 获取条口记录结果（露珠图数据）

**消息类型：** `get_hand_records_result`

**说明：** 查询hand_records中当前桌台当前账期所有场次局数的结果result_1，用于前端生成露珠图。系统会根据客户端IP自动获取对应的桌台信息

**请求消息：**
```json
{
  "type": "get_hand_records_result",
  "data": {
    "account_period": "********"
  }
}
```

**请求参数说明：**
- `account_period`: 账期，格式为YYYYMMDD（必需）

**成功响应：**
```json
{
  "type": "get_hand_records_result_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "message": "查询成功",
    "table_id": 1,
    "table_code": "A01",
    "records": [
      {
        "id": 1,
        "table_id": 1,
        "account_period": "********",
        "shoe_no": 1,
        "hand_no": 1,
        "result_1": "庄",
        "create_time": "2024-03-20 10:15:30"
      },
      {
        "id": 2,
        "table_id": 1,
        "account_period": "********",
        "shoe_no": 1,
        "hand_no": 2,
        "result_1": "闲",
        "create_time": "2024-03-20 10:18:45"
      }
    ],
    "total_count": 2,
    "client_ip": "*************"
  },
  "timestamp": **********
}
```

**错误响应：**
```json
{
  "type": "get_hand_records_result_error",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "error": "账期不能为空",
    "client_ip": "*************"
  },
  "timestamp": **********
}
```

**响应数据说明：**
- `records`: 条口记录数组，按场次编号(shoe_no)和局号编号(hand_no)升序排列
- `result_1`: 游戏结果（如：庄、闲、和等）
- `total_count`: 总记录数

**其他可能的错误信息：**
- "用户未登录"
- "令牌无效，请重新登录"
- "账期格式错误，请使用 YYYYMMDD 格式"
- "获取桌台信息失败"
- "查询条口记录失败"





## 字段说明

### 用户信息字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 用户ID |
| username | string | 用户名 |
| realname | string | 真实姓名 |
| nickname | string | 昵称 |
| gender | int | 性别：1-男;2-女;3-保密 |
| gender_name | string | 性别名称 |
| avatar | string | 头像地址 |
| mobile | string | 手机号码 |
| email | string | 邮箱地址 |
| dept_id | int | 部门ID |
| status | int | 状态：1-正常;2-禁用 |
| status_name | string | 状态名称 |
| login_time | uint | 最近登录时间 |
| login_ip | string | 最近登录IP |
| create_time | uint | 创建时间 |



### 桌台信息字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 桌台ID |
| table_code | string | 桌台编号 |
| table_name | string | 桌台名称 |
| game_type | int | 游戏类型：1-百家乐;2-龙虎斗;3-百家乐免佣;4-牛牛;5-三公;6-A89;7-庄闲牛;8-骰宝;9-轮盘 |
| game_type_name | string | 游戏类型名称 |
| table_ip | string | 桌台IP地址 |
| video_url | string | 视频地址 |
| channel | int | 业务通道：1-现场;2-电投;3-网投 |
| channel_name | string | 通道名称 |
| wash_user_type | string | 参与洗码用户类型 |
| wash_rate | float | 洗码率 |
| max_bet_u | float | U码最大下注金额 |
| max_bet_cash | float | 现金最大下注金额 |
| max_bet_chips | float | 筹码最大下注金额 |
| tie_rate | float | 龙虎和底返 |
| status | int | 状态：1-禁用;2-启用 |
| status_name | string | 状态名称 |
| memo | string | 备注 |
| bets | array | 下注配置列表 |
| create_time | string | 创建时间 |

### 下注配置字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 配置ID |
| table_id | int | 桌台ID |
| bet_area | string | 下注区域 |
| odds | float | 赔率 |
| max_bet_u | float | U码最大下注金额 |
| max_bet_cash | float | 现金最大下注金额 |
| max_bet_chips | float | 筹码最大下注金额 |
| create_time | string | 创建时间 |

### 桌台开台数据字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int64 | 主键ID |
| table_id | int64 | 桌台ID |
| account_period | string | 账期 |
| game_type | int8 | 游戏类型：1-百家乐;2-龙虎斗;3-百家乐免佣;4-牛牛;5-三公;6-A89;7-庄闲牛;8-骰宝;9-轮盘 |
| game_type_name | string | 游戏类型名称 |
| stats | int8 | 当前状态：1-销售中;2-等待洗牌;3-等待出码;4-已收盘 |
| stats_name | string | 状态名称 |
| shoe_no | int | 场次编号 |
| hand_no | int | 局号编号 |
| create_time | string | 创建时间 |

#### 4. 获取桌台情况统计

**消息类型：** `get_table_stats`

**说明：** 获取指定账期的桌台情况统计信息，包括最新洗牌信息、财务统计（按货币类型分组）、手牌统计（按货币类型分组）等。系统会根据客户端IP自动获取对应的桌台信息

**请求消息：**
```json
{
  "type": "get_table_stats",
  "data": {
    "account_period": "2025-01-08"
  }
}
```

**请求参数说明：**
- `account_period`: 账期，格式为YYYY-MM-DD（必需）

**成功响应：**
```json
{
  "type": "get_table_stats_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "message": "获取桌台统计成功",
    "table_id": 1,
    "table_code": "A01",
    "account_period": "2025-01-08",
    "shuffle_info": {
      "shuffle_method": "自动洗牌",
      "table_poker": "张三",
      "cut_card_dealer": "李四",
      "shoe_no": 3,
      "card_no": 1,
      "create_time": "2025-01-08 10:30:00"
    },
    "finance_stats": {
      "筹码": {
        "currency_type": 1,
        "currency_type_name": "筹码",
        "out_code_amount": 500000.00,
        "add_code_amount": 50000.00
      },
      "现金": {
        "currency_type": 2,
        "currency_type_name": "现金",
        "out_code_amount": 200000.00,
        "add_code_amount": 20000.00
      },
      "U码": {
        "currency_type": 3,
        "currency_type_name": "U码",
        "out_code_amount": 100000.00,
        "add_code_amount": 10000.00
      }
    },
    "hand_stats": {
      "筹码": {
        "currency_type": 1,
        "currency_type_name": "筹码",
        "total_wash_amount": 8000.00,
        "total_tip_amount": 500.00,
        "total_win_loss": -15000.00,
        "latest_win_loss": -3000.00,
        "total_code_amount": 565000.00
      },
      "现金": {
        "currency_type": 2,
        "currency_type_name": "现金",
        "total_wash_amount": 3500.00,
        "total_tip_amount": 200.00,
        "total_win_loss": -8000.00,
        "latest_win_loss": -1500.00,
        "total_code_amount": 228000.00
      },
      "U码": {
        "currency_type": 3,
        "currency_type_name": "U码",
        "total_wash_amount": 1000.00,
        "total_tip_amount": 100.00,
        "total_win_loss": -2000.00,
        "latest_win_loss": -500.00,
        "total_code_amount": 112000.00
      }
    }
  },
  "timestamp": **********
}
```

**响应数据说明：**

**基本信息：**
- `table_id`: 桌台ID
- `table_code`: 桌台编号
- `account_period`: 账期

**洗牌信息 (shuffle_info)：**
- `shuffle_method`: 洗牌方式
- `table_poker`: 台面洗牌牌手
- `cut_card_dealer`: 切牌人
- `shoe_no`: 场次编号
- `card_no`: 牌次号
- `create_time`: 创建时间

**财务统计 (finance_stats)：**
按货币类型分组，每种货币类型包含：
- `currency_type`: 货币类型（1-筹码，2-现金，3-U码）
- `currency_type_name`: 货币类型名称
- `out_code_amount`: 当前账期审核通过的出码量
- `add_code_amount`: 当前账期审核通过的加彩量

**手牌统计 (hand_stats)：**
按货币类型分组，每种货币类型包含：
- `currency_type`: 货币类型（1-筹码，2-现金，3-U码）
- `currency_type_name`: 货币类型名称
- `total_wash_amount`: 本期洗码量合计
- `total_tip_amount`: 小费合计
- `total_win_loss`: 输赢金额合计
- `latest_win_loss`: 最新一局的输赢金额
- `total_code_amount`: 总码量（出码量+加彩量-输赢金额合计）

**错误响应：**
```json
{
  "type": "get_table_stats_error",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "error": "account_period 参数不能为空"
  },
  "timestamp": **********
}
```

**其他可能的错误信息：**
- "用户未登录"
- "令牌无效，请重新登录"
- "账期格式错误，请使用 YYYY-MM-DD 格式"
- "获取桌台信息失败"
- "获取洗牌信息失败"
- "获取财务统计失败"
- "获取手牌统计失败"

### 客户信息接口

#### 1. 通过洗码号获取客户信息

**消息类型：** `get_user_by_wash_code`

**说明：** 根据洗码号查询客户详细信息，要求客户账号状态必须是正常

**请求消息：**
```json
{
  "type": "get_user_by_wash_code",
  "data": {
    "wash_code": "WM001"
  }
}
```

**成功响应：**
```json
{
  "type": "get_user_by_wash_code_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "user_info": {
      "id": 1,
      "wash_code": "WM001",
      "account_type": 1,
      "account_type_name": "主号",
      "main_code": "",
      "name": "张三",
      "phone": "***********",
      "user_type": 1,
      "user_type_name": "公司客户",
      "rebate_rate": 0.0180,
      "rebate_rate_text": "1.80%",
      "is_dragon_tiger": 1,
      "dragon_tiger_text": "是",
      "status": 1,
      "status_name": "正常",
      "memo": "VIP客户",
      "operator_remark": "优质客户",
      "create_time": "2024-01-01 10:00:00",
      "again_create_time": "2024-01-01 10:00:00",
      "update_time": "2024-01-01 15:30:00"
    },
    "wash_code": "WM001"
  },
  "timestamp": **********
}
```

**错误响应：**
```json
{
  "type": "get_user_by_wash_code_error",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "error": "未找到洗码号为 WM001 的客户",
    "wash_code": "WM001"
  },
  "timestamp": **********
}
```

### 客户信息字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int64 | 客户ID |
| wash_code | string | 洗码号，用于识别客户身份 |
| account_type | int8 | 账号类型：1-主号;2-分线 |
| account_type_name | string | 账号类型名称 |
| main_code | string | 所属主号的洗码号 |
| name | string | 客户姓名 |
| phone | string | 联系电话 |
| user_type | int8 | 客户类型：1-公司客户;2-返点F;3-返点W;4-占成客户;5-特殊客户 |
| user_type_name | string | 客户类型名称 |
| rebate_rate | float | 返点比例 |
| rebate_rate_text | string | 返点比例文本（百分比格式） |
| is_dragon_tiger | int8 | 是否龙虎洗码：0-否;1-是 |
| dragon_tiger_text | string | 龙虎洗码文本 |
| status | int8 | 账号状态：1-正常;2-冻结;3-禁用 |
| status_name | string | 账号状态名称 |
| memo | string | 客户备注信息 |
| operator_remark | string | 操作人员备注 |
| create_time | string | 创建时间 |
| again_create_time | string | 重开户时间 |
| update_time | string | 最后更新时间 |

### 系统用户查询接口

#### 1. 通过员工编号查询系统用户信息

**消息类型：** `get_sys_user_by_serial`

**说明：** 通过员工编号查询系统用户基本信息，仅返回状态正常的用户

**业务逻辑：**
- 根据员工编号查询sys_user表
- 仅返回状态为正常(status=1)且未删除(mark=1)的用户
- 只返回主键编号、员工编号、真实姓名三个字段

**请求消息：**
```json
{
  "type": "get_sys_user_by_serial",
  "data": {
    "serial_number": 1001
  }
}
```

**成功响应：**
```json
{
  "type": "get_sys_user_by_serial_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "sys_user": {
      "id": 1,
      "serial_number": 1001,
      "realname": "张三"
    },
    "message": "查询成功"
  },
  "timestamp": **********
}
```

**错误响应：**
```json
{
  "type": "get_sys_user_by_serial_error",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "error": "未找到员工编号为 1001 的正常状态用户",
    "serial_number": 1001
  },
  "timestamp": **********
}
```

**其他可能的错误信息：**
- "用户未登录"
- "令牌无效，请重新登录"
- "员工编号不能为空或无效"
- "查询系统用户信息失败"

### 洗牌开场接口

#### 1. 洗牌开场

**消息类型：** `start_shuffle`

**说明：** 开始洗牌并记录洗牌信息，同时更新桌台账期状态

**业务逻辑：**
- 保存一条洗牌记录到shuffle_records表
- 更新tables_start中当前桌台当前账期的场次编号累加1
- 将局号编号初始化为0
- 设置桌台状态为"销售中"

**请求消息：**
```json
{
  "type": "start_shuffle",
  "data": {
    "account_period": "2024-01-15",
    "shift": 1,
    "shuffle_method": "手动洗牌",
    "card_color": "红色",
    "monitor_id": "M001",
    "monitor_name": "张监场",
    "admin_id": "A001", 
    "admin_name": "王管理",
    "shuffle_table_poker": "李洗牌",
    "table_poker": "刘台手",
    "monitor_poker": "陈监牌",
    "cut_card_dealer": "赵切牌"
  }
}
```

**说明：** 桌台ID将通过客户端IP自动获取，无需在请求中传递table_id参数。

**成功响应：**
```json
{
  "type": "start_shuffle_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "shuffle_record": {
      "id": 1,
      "table_id": 1,
      "account_period": "2024-01-15",
      "shoe_no": 1,
      "card_no": 1,
      "shift": 1,
      "shift_name": "早班",
      "shuffle_method": "手动洗牌",
      "card_color": "红色",
      "monitor_id": "M001",
      "monitor_name": "张监场",
      "admin_id": "A001",
      "admin_name": "王管理",
      "shuffle_table_poker": "李洗牌",
      "table_poker": "刘台手",
      "monitor_poker": "陈监牌",
      "cut_card_dealer": "赵切牌",
      "create_time": "2024-01-15 14:30:00"
    },
    "tables_start": {
      "id": 1,
      "table_id": 1,
      "account_period": "2024-01-15",
      "game_type": 1,
      "game_type_name": "百家乐",
      "stats": 1,
      "stats_name": "销售中",
      "shoe_no": 1,
      "hand_no": 0,
      "create_time": "2024-01-15 14:30:00"
    },
    "message": "洗牌开场成功"
  },
  "timestamp": **********
}
```

**错误响应：**
```json
{
  "type": "start_shuffle_error",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "error": "桌台不存在或未启用"
  },
  "timestamp": **********
}
```

**其他可能的错误信息：**
- "用户未登录"
- "令牌无效，请重新登录"
- "桌台ID不能为空"
- "账期不能为空"
- "班次无效"
- "账期格式错误，请使用 YYYY-MM-DD 格式"
- "创建洗牌记录失败"
- "更新桌台账期记录失败"

### 出码申请接口

#### 1. 申请出码/加彩

**消息类型：** `apply_out_code`

**说明：** 批量提交出码或加彩申请，支持同时申请多种货币类型，操作人为当前登录用户的真实姓名

**业务规则：**
- **出码操作(operation_type=1)：** 状态直接设置为审核通过(2)，每个桌台每个账期每个货币类型只能有一条同意状态的出码记录，申请成功后系统会自动更新对应桌台账期的状态为"等待洗牌"
- **加彩操作(operation_type=3)：** 状态设置为申请中(1)，可以多次申请，没有限制，不会更新桌台状态

**请求消息：**
```json
{
  "type": "apply_out_code",
  "data": {
    "operation_type": 1,
    "account_period": "2024-01-15",
    "amounts": [
      {
        "currency_type": 1,
        "total_amount": 50000.00
      },
      {
        "currency_type": 2,
        "total_amount": 30000.00
      },
      {
        "currency_type": 3,
        "total_amount": 20000.00
      }
    ]
  }
}
```

**参数说明：**
- `operation_type`: 操作类型，1-出码，3-加彩

**说明：** 桌台ID将通过客户端IP自动获取，无需在请求中传递table_id参数。

**成功响应：**
```json
{
  "type": "apply_out_code_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "out_code_list": [
      {
        "id": 1,
        "table_id": 1,
        "account_period": "2024-01-15",
        "type": 1,
        "type_name": "出码",
        "status": 2,
        "status_name": "同意",
        "currency_type": 1,
        "currency_name": "筹码",
        "total_amount": 50000.00,
        "operator": "张三",
        "approver": "张三",
        "create_time": "2024-01-15 14:30:00"
      },
      {
        "id": 2,
        "table_id": 1,
        "account_period": "2024-01-15",
        "type": 1,
        "type_name": "出码",
        "status": 2,
        "status_name": "同意",
        "currency_type": 2,
        "currency_name": "现金",
        "total_amount": 30000.00,
        "operator": "张三",
        "approver": "张三",
        "create_time": "2024-01-15 14:30:00"
      },
      {
        "id": 3,
        "table_id": 1,
        "account_period": "2024-01-15",
        "type": 1,
        "type_name": "出码",
        "status": 2,
        "status_name": "同意",
        "currency_type": 3,
        "currency_name": "U码",
        "total_amount": 20000.00,
        "operator": "张三",
        "approver": "张三",
        "create_time": "2024-01-15 14:30:00"
      }
    ],
    "message": "出码申请提交成功"
  },
  "timestamp": **********
}
```

**错误响应：**
```json
{
  "type": "apply_out_code_error",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "error": "桌台T001在账期2024-01-15已存在筹码的出码记录"
  },
  "timestamp": **********
}
```

**其他可能的错误信息：**
- "桌台不存在或未启用"
- "账期格式错误，请使用 YYYY-MM-DD 格式"
- "货币类型无效"
- "申请金额必须大于0"
- "不能重复申请同一种货币类型"
- "更新桌台状态失败"

#### 2. 获取出码申请列表

**消息类型：** `get_out_code_list`

**说明：** 获取出码申请列表，支持按桌台ID和状态过滤

**请求消息：**
```json
{
  "type": "get_out_code_list",
  "data": {
    "table_id": 1,
    "status": 1,
    "limit": 20,
    "offset": 0
  }
}
```

**成功响应：**
```json
{
  "type": "get_out_code_list_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "list": [
      {
        "id": 1,
        "table_id": 1,
        "account_period": "2024-01-15",
        "type": 1,
        "type_name": "出码",
        "status": 1,
        "status_name": "申请中",
        "currency_type": 1,
        "currency_name": "筹码",
        "total_amount": 50000.00,
        "operator": "张三",
        "approver": "",
        "create_time": "2024-01-15 14:30:00"
      }
    ],
    "total": 1,
    "limit": 20,
    "offset": 0
  },
  "timestamp": **********
}
```

**错误响应：**
```json
{
  "type": "get_out_code_list_error",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "error": "用户未登录"
  },
  "timestamp": **********
}
```

#### 3. 获取出码申请详情

**消息类型：** `get_out_code_detail`

**说明：** 根据申请ID获取出码申请的详细信息

**请求消息：**
```json
{
  "type": "get_out_code_detail",
  "data": {
    "id": 1
  }
}
```

**成功响应：**
```json
{
  "type": "get_out_code_detail_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "out_code_info": {
      "id": 1,
      "table_id": 1,
      "account_period": "2024-01-15",
      "type": 1,
      "type_name": "出码",
      "status": 1,
      "status_name": "申请中",
      "currency_type": 1,
      "currency_name": "筹码",
      "total_amount": 50000.00,
      "operator": "张三",
      "approver": "",
      "create_time": "2024-01-15 14:30:00"
    }
  },
  "timestamp": **********
}
```

**错误响应：**
```json
{
  "type": "get_out_code_detail_error",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "error": "未找到ID为 1 的出码申请",
    "id": 1
  },
  "timestamp": **********
}
```



### 出码申请字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 申请ID |
| table_id | int | 桌台ID |
| account_period | string | 账期（格式：YYYYMMDD） |
| type | int8 | 操作类型：1-出码;2-收码;3-加彩 |
| type_name | string | 操作类型名称 |
| status | int8 | 状态：1-申请中;2-同意;3-拒绝 |
| status_name | string | 状态名称 |
| currency_type | int8 | 货币类型：1-筹码;2-现金;3-U码 |
| currency_name | string | 货币类型名称 |
| total_amount | float64 | 总金额 |
| operator | string | 操作人员姓名 |
| approver | string | 审核人员姓名 |
| create_time | string | 创建时间 |

### 点码查询接口

#### 1. 点码查询

**消息类型：** `get_code_summary`

**说明：** 查询指定桌台和账期的点码汇总信息，按货币类型分组统计

**业务逻辑：**
- 通过桌台ID、账期查询finance_records的出码量（type=1, status=2）
- 通过桌台ID、账期查询finance_records的加彩（type=3, status=2）
- 通过桌台ID、账期查询hand_records的客人输赢、小费
- 按货币类型（1-筹码，2-现金，3-U码）分组统计
- 计算总码量 = 出码量 + 加彩 - 客人输赢
- 计算码盘上下水 = 总码量 - 小费

**请求消息：**
```json
{
  "type": "get_code_summary",
  "data": {
    "account_period": "202406"
  }
}
```

**说明：** 桌台ID将通过客户端IP自动获取，无需在请求中传递table_id参数。

**成功响应：**
```json
{
  "type": "get_code_summary_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "table_id": 1,
    "account_period": "202406",
    "items": [
      {
        "currency_type": 1,
        "out_amount": 100000.00,
        "bonus_amount": 5000.00,
        "total_amount": 95000.00,
        "win_loss": 10000.00,
        "tip_amount": 2000.00,
        "water_amount": 93000.00
      },
      {
        "currency_type": 2,
        "out_amount": 50000.00,
        "bonus_amount": 2000.00,
        "total_amount": 48000.00,
        "win_loss": 4000.00,
        "tip_amount": 1000.00,
        "water_amount": 47000.00
      },
      {
        "currency_type": 3,
        "out_amount": 30000.00,
        "bonus_amount": 1000.00,
        "total_amount": 29000.00,
        "win_loss": 2000.00,
        "tip_amount": 500.00,
        "water_amount": 28500.00
      }
    ]
  },
  "timestamp": **********
}
```

**错误响应：**
```json
{
  "type": "get_code_summary_error",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "error": "用户未登录"
  },
  "timestamp": **********
}
```

**其他可能的错误信息：**
- "令牌无效，请重新登录"
- "获取桌台信息失败"
- "账期不能为空"
- "查询点码汇总失败"

### 点码查询字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| account_period | string | 账期（格式：YYYYMM） |
| items | array | 按货币类型分组的数据数组 |
| currency_type | int8 | 货币类型：1-筹码;2-现金;3-U码 |
| out_amount | float64 | 出码量（来自finance_records，type=1, status=2） |
| bonus_amount | float64 | 加彩（来自finance_records，type=3, status=2） |
| total_amount | float64 | 总码量（计算公式：出码量+加彩-客人输赢） |
| win_loss | float64 | 客人输赢（来自hand_records，正数为赢，负数为输） |
| tip_amount | float64 | 小费（来自hand_records） |
| water_amount | float64 | 码盘上下水（计算公式：总码量-小费） |

### 下注录入接口







#### 1. 按桌台整体保存批量下注录入

**消息类型：** `table_batch_bet_entry`

**说明：** 按桌台整体保存批量下注记录，通过桌台编号、账期、场次、局数进行覆盖操作

**业务逻辑：**
- 按桌台整体保存下注记录到Redis中，设置24小时过期时间
- Redis键名格式：`table_bet_record:{table_id}:{account_period}:{round_no}:{hand_no}`
- 每次提交都会覆盖该桌台该局的所有下注记录
- 录入结果时，系统会从Redis获取该局的所有下注记录进行结算
- 结算完成后，记录会从Redis删除并保存到数据库
- 自动计算洗码量、洗码费等字段

**请求消息：**
```json
{
  "type": "table_batch_bet_entry",
  "data": {
    "table_bet_record": {
      "account_period": "********",
      "round_no": 1,
      "hand_no": 1,
      "bet_records": [
        {
          "game_type": 1,
          "account_period": "********",
          "round_no": 1,
          "hand_no": 1,
          "wash_code": "WC001",
          "user_name": "张三",
          "currency_type": 1,
          "banker_amount": 1000.00,
          "player_amount": 500.00,
          "tie_amount": 100.00,
          "banker_pair_amount": 50.00,
          "player_pair_amount": 50.00,
          "lucky_6_amount": 0.00,
          "lucky_7_amount": 0.00,
          "win_result": "",
          "win_loss": 0.00,
          "loss": 0.00,
          "amount_tip": 0.00,
          "amount_bottom": 0.00,
          "wash_rate": 0.00018,
          "wash_amount": 1700.00,
          "wash_tip": 0.31
        },
        {
          "game_type": 1,
          "account_period": "********",
          "round_no": 1,
          "hand_no": 1,
          "wash_code": "WC002",
          "user_name": "李四",
          "currency_type": 2,
          "banker_amount": 800.00,
          "player_amount": 600.00,
          "tie_amount": 80.00,
          "banker_pair_amount": 40.00,
          "player_pair_amount": 40.00,
          "lucky_6_amount": 0.00,
          "lucky_7_amount": 0.00,
          "win_result": "",
          "win_loss": 0.00,
          "loss": 0.00,
          "amount_tip": 0.00,
          "amount_bottom": 0.00,
          "wash_rate": 0.00018,
          "wash_amount": 1560.00,
          "wash_tip": 0.28
        }
      ]
    }
  }
}
```

**说明：** 桌台ID将通过客户端IP自动获取，无需在请求中传递table_id参数。下注记录中的table_id字段也会在服务端自动设置。

**成功响应：**
```json
{
  "type": "table_batch_bet_entry_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "result": {
      "success_count": 2,
      "message": "按桌台整体保存成功，共保存2条下注记录到Redis",
      "table_id": 1,
      "account_period": "********",
      "round_no": 1,
      "hand_no": 1
    },
    "message": "按桌台整体保存成功，共保存2条下注记录到Redis"
  },
  "timestamp": **********
}
```

**错误响应：**
```json
{
  "type": "table_batch_bet_entry_error",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "error": "用户未登录"
  },
  "timestamp": **********
}
```

**其他可能的错误信息：**
- "令牌无效，请重新登录"
- "获取桌台信息失败"
- "下注记录不能为空"
- "批量下注记录数量不能超过100条"
- "桌台不存在或未启用"
- "账期格式错误，请使用 YYYYMMDD 格式"
- "第X条记录验证失败: 具体错误信息"
- "第X条记录的账期不一致"
- "第X条记录的场次编号不一致"
- "第X条记录的局号编号不一致"
- "保存到Redis失败: 具体错误信息"

### 批量下注字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| game_type | int8 | 游戏类型：1-百家乐;2-龙虎斗;3-百家乐免佣;4-牛牛;5-三公;6-A89;7-庄闲牛;8-骰宝;9-轮盘 |
| account_period | string | 账期（格式：YYYYMMDD） |
| round_no | int | 场次编号 |
| hand_no | int | 局号编号 |
| wash_code | string | 洗码号 |
| user_name | string | 客户姓名 |
| currency_type | int8 | 货币类型：1-筹码;2-现金;3-U码 |
| banker_amount | float64 | 庄金额 |
| player_amount | float64 | 闲金额 |
| tie_amount | float64 | 和金额 |
| banker_pair_amount | float64 | 庄对金额 |
| player_pair_amount | float64 | 闲对金额 |
| lucky_6_amount | float64 | 幸运6金额 |
| lucky_7_amount | float64 | 幸运7金额 |
| win_result | string | 结果 |
| win_loss | float64 | 输赢金额 |
| loss | float64 | 输口 |
| amount_tip | float64 | 小费金额 |
| amount_bottom | float64 | 和底 |
| wash_rate | float64 | 洗码率 |
| wash_amount | float64 | 本局洗码量 |
| wash_tip | float64 | 洗码费 |

### 按桌台整体保存批量下注字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| table_bet_record | object | 桌台下注记录对象 |
| account_period | string | 账期（格式：YYYYMMDD） |
| round_no | int | 场次编号 |
| hand_no | int | 局号编号 |
| bet_records | array | 该局的所有下注记录数组 |

### 按桌台整体保存批量下注响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success_count | int | 成功保存的记录数量 |
| message | string | 操作结果消息 |
| table_id | int64 | 桌台ID |
| account_period | string | 账期 |
| round_no | int | 场次编号 |
| hand_no | int | 局号编号 |

### 百家乐结果录入接口

#### 1. 百家乐结果录入与结算

**消息类型：** `enter_result`

**说明：** 录入百家乐游戏结果并进行结算，更新下注记录、统计局口记录、更新桌台局号

**业务逻辑：**
- 从Redis获取本局所有下注记录
- 根据结果计算每条下注的输赢、洗码、小费等
- 按百家乐规则计算赔率（庄1:0.95按每满100计算，闲1:1，和1:8，庄对/闲对1:11）
- 检查数据库避免重复结算
- 保存结算结果到bet_records表
- 按货币类型统计到hand_records
- 更新tables_start的局号编号+1
- 从Redis删除已处理的记录

**百家乐结算规则：**
- 庄赔率：每满100按1:0.95，不足100部分按1:1
- 闲赔率：1:1
- 和赔率：1:8
- 庄对/闲对赔率：1:11
- 结果开出和、庄对、闲对时底注算小费
- 输口=洗码量，洗码率从桌台配置中获取，洗码费=洗码量×洗码率

**请求消息：**
```json
{
  "type": "enter_result",
  "data": {
    "table_id": 1,
    "account_period": "202406",
    "round_no": 1,
    "hand_no": 1,
    "result": ["庄", "庄对"]
  }
}
```

**成功响应：**
```json
{
  "type": "enter_result_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "message": "结算成功",
    "processed_count": 1,
    "hand_summary": {
      "1": {
        "table_id": 1,
        "account_period": "202406",
        "shoe_no": 1,
        "hand_no": 1,
        "game_type": 1,
        "currency_type": 1,
        "bet_amount": 1700.00,
        "result_1": "庄,庄对",
        "win_loss": 1047.50,
        "loss": 1700.00,
        "amount_tip": 1050.00,
        "amount_bottom": 0.00,
        "wash_rate": 0.00018,
        "wash_amount": 1700.00,
        "wash_tip": 0.31
      }
    }
  },
  "timestamp": **********
}
```

**错误响应：**
```json
{
  "type": "enter_result_error",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "error": "该局没有找到下注记录"
  },
  "timestamp": **********
}
```

**其他可能的错误信息：**
- "用户未登录"
- "令牌无效，请重新登录"
- "桌台ID不能为空"
- "账期不能为空"
- "场次编号不能为空"
- "局号编号不能为空"
- "结果不能为空"
- "百家乐结果录入失败"

### 批量点码接口

#### 1. 批量点码

**消息类型：** `batch_settlement`

**说明：** 批量保存点码记录，支持多种货币类型同时点码，保存完成后自动更新桌台状态

**业务逻辑：**
- 批量保存点码记录到settlement_records表
- 校验同一桌台ID、场次编号、账期、货币类型只能保存一条数据
- 支持多种货币类型同时点码
- 保存完成后根据桌台ID和账期修改TablesStart状态为"等待洗牌"
- 使用事务确保数据一致性

**请求消息：**
```json
{
  "type": "batch_settlement",
  "data": {
    "settlement_records": [
      {
        "shoe_no": 1,
        "account_period": "202406",
        "currency_type": 1,
        "chips_20w": 2,
        "chips_10w": 5,
        "chips_5w": 10,
        "chips_1w": 20,
        "chips_5k": 50,
        "chips_1k": 100,
        "chips_500": 200,
        "chips_100": 500,
        "chips_50": 1000,
        "chips_10": 5000,
        "chips_5": 10000,
        "total_amount": 1000000.00,
        "client_win_loss": 50000.00,
        "amount_tip": 5000.00,
        "amount_bottom": 0.00,
        "wash_rate": 0.0180,
        "wash_amount": 800000.00,
        "wash_tip": 144.00,
        "compare_result": "点码与注单比对正常",
        "remark": "第一场次点码",
        "operator": "张三"
      },
      {
        "shoe_no": 1,
        "account_period": "202406",
        "currency_type": 2,
        "chips_20w": 1,
        "chips_10w": 3,
        "chips_5w": 5,
        "chips_1w": 10,
        "chips_5k": 20,
        "chips_1k": 50,
        "chips_500": 100,
        "chips_100": 200,
        "chips_50": 500,
        "chips_10": 1000,
        "chips_5": 2000,
        "total_amount": 500000.00,
        "client_win_loss": 25000.00,
        "amount_tip": 3000.00,
        "amount_bottom": 0.00,
        "wash_rate": 0.0180,
        "wash_amount": 400000.00,
        "wash_tip": 72.00,
        "compare_result": "点码与注单比对正常",
        "remark": "第一场次点码",
        "operator": "张三"
      }
    ]
  }
}
```

**说明：** 桌台ID将通过客户端IP自动获取，无需在请求中传递table_id参数。

**成功响应：**
```json
{
  "type": "batch_settlement_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "message": "批量点码成功",
    "settlement_records": [
      {
        "id": 1,
        "table_id": 1,
        "shoe_no": 1,
        "account_period": "2024-06-01",
        "currency_type": 1,
        "chips_20w": 2,
        "chips_10w": 5,
        "chips_5w": 10,
        "chips_1w": 20,
        "chips_5k": 50,
        "chips_1k": 100,
        "chips_500": 200,
        "chips_100": 500,
        "chips_50": 1000,
        "chips_10": 5000,
        "chips_5": 10000,
        "total_amount": 1000000.00,
        "client_win_loss": 50000.00,
        "amount_tip": 5000.00,
        "amount_bottom": 0.00,
        "wash_rate": 0.0180,
        "wash_amount": 800000.00,
        "wash_tip": 144.00,
        "compare_result": "点码与注单比对正常",
        "remark": "第一场次点码",
        "operator": "张三",
        "create_time": "2024-06-01 10:00:00"
      },
      {
        "id": 2,
        "table_id": 1,
        "shoe_no": 1,
        "account_period": "2024-06-01",
        "currency_type": 2,
        "chips_20w": 1,
        "chips_10w": 3,
        "chips_5w": 5,
        "chips_1w": 10,
        "chips_5k": 20,
        "chips_1k": 50,
        "chips_500": 100,
        "chips_100": 200,
        "chips_50": 500,
        "chips_10": 1000,
        "chips_5": 2000,
        "total_amount": 500000.00,
        "client_win_loss": 25000.00,
        "amount_tip": 3000.00,
        "amount_bottom": 0.00,
        "wash_rate": 0.0180,
        "wash_amount": 400000.00,
        "wash_tip": 72.00,
        "compare_result": "点码与注单比对正常",
        "remark": "第一场次点码",
        "operator": "张三",
        "create_time": "2024-06-01 10:00:00"
      }
    ]
  },
  "timestamp": **********
}
```

**错误响应：**
```json
{
  "type": "batch_settlement_error",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "error": "桌台1场次1账期202406货币类型1已存在点码记录"
  },
  "timestamp": **********
}
```

**其他可能的错误信息：**
- "用户未登录"
- "令牌无效，请重新登录"
- "点码记录不能为空"
- "第1条记录验证失败: 桌台ID不能为空"
- "第1条记录验证失败: 场次编号不能为空"
- "第1条记录验证失败: 账期不能为空"
- "第1条记录验证失败: 货币类型无效"
- "第1条记录验证失败: 总额不能为负数"
- "保存点码记录失败"
- "更新桌台状态失败"
- "提交事务失败"

### 批量点码字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| settlement_records | array | 点码记录数组 |
| shoe_no | int | 场次编号 |
| account_period | string | 账期（格式：YYYYMMDD） |
| currency_type | int8 | 货币类型：1-筹码;2-现金;3-U码 |
| chips_20w | int | 筹码20万数量 |
| chips_10w | int | 筹码10万数量 |
| chips_5w | int | 筹码5万数量 |
| chips_1w | int | 筹码1万数量 |
| chips_5k | int | 筹码5千数量 |
| chips_1k | int | 筹码1千数量 |
| chips_500 | int | 筹码500数量 |
| chips_100 | int | 筹码100数量 |
| chips_50 | int | 筹码50数量 |
| chips_10 | int | 筹码10数量 |
| chips_5 | int | 筹码5数量 |
| total_amount | float64 | 总额 |
| client_win_loss | float64 | 客户输赢总和 |
| amount_tip | float64 | 小费金额 |
| amount_bottom | float64 | 和底 |
| wash_rate | float64 | 洗码率 |
| wash_amount | float64 | 本局洗码量 |
| wash_tip | float64 | 洗码费 |
| compare_result | string | 点码与注单比对结果 |
| remark | string | 备注信息 |
| operator | string | 操作人员 |

### 点码记录响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 点码记录ID |
| table_id | int64 | 桌台ID |
| shoe_no | int | 场次编号 |
| account_period | string | 账期（格式：YYYY-MM-DD） |
| currency_type | int8 | 货币类型：1-筹码;2-现金;3-U码 |
| chips_20w | int | 筹码20万数量 |
| chips_10w | int | 筹码10万数量 |
| chips_5w | int | 筹码5万数量 |
| chips_1w | int | 筹码1万数量 |
| chips_5k | int | 筹码5千数量 |
| chips_1k | int | 筹码1千数量 |
| chips_500 | int | 筹码500数量 |
| chips_100 | int | 筹码100数量 |
| chips_50 | int | 筹码50数量 |
| chips_10 | int | 筹码10数量 |
| chips_5 | int | 筹码5数量 |
| total_amount | float64 | 总额 |
| client_win_loss | float64 | 客户输赢总和 |
| amount_tip | float64 | 小费金额 |
| amount_bottom | float64 | 和底 |
| wash_rate | float64 | 洗码率 |
| wash_amount | float64 | 本局洗码量 |
| wash_tip | float64 | 洗码费 |
| compare_result | string | 点码与注单比对结果 |
| remark | string | 备注信息 |
| operator | string | 操作人员 |
| create_time | string | 创建时间（格式：YYYY-MM-DD HH:mm:ss） |

### 收盘接口

#### 1. 收盘功能

**消息类型：** `close_table`

**说明：** 将当前桌台最新的账期记录状态改为已收盘，并创建新的账期记录

**业务逻辑：**
- 验证桌台是否存在且启用
- 查找当前桌台最新的账期记录
- 检查当前账期记录是否已经是已收盘状态，防止重复收盘
- 将当前账期记录状态更新为"已收盘"
- 创建新的账期记录，状态为"等待出码"，场次编号和局号编号从0开始
- 返回新创建的账期记录信息

**请求消息：**
```json
{
  "type": "close_table",
  "data": {
  }
}
```

**说明：** 桌台ID将通过客户端IP自动获取，无需在请求中传递table_id参数。

**成功响应：**
```json
{
  "type": "close_table_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "message": "收盘成功",
    "tables_start": {
      "id": 1,
      "table_id": 1,
      "account_period": "********",
      "game_type": 1,
      "game_type_name": "百家乐",
      "stats": 3,
      "stats_name": "等待出码",
      "shoe_no": 0,
      "hand_no": 0,
      "create_time": "2025-07-11 10:00:00"
    }
  },
  "timestamp": **********
}
```

**错误响应：**
```json
{
  "type": "close_table_error",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "error": "桌台1在账期********已经是已收盘状态"
  },
  "timestamp": **********
}
```

**其他可能的错误信息：**
- "用户未登录"
- "令牌无效，请重新登录"
- "获取桌台信息失败"
- "桌台不存在或未启用"
- "未找到桌台的账期记录"
- "更新桌台账期记录失败"
- "创建新账期记录失败"

### 收盘字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| 无 | - | 桌台ID将通过客户端IP自动获取 |

### 收盘响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| message | string | 操作结果消息 |
| tables_start | object | 桌台账期信息 |
| id | int | 桌台账期记录ID |
| table_id | int64 | 桌台ID |
| account_period | string | 账期（格式：YYYYMMDD） |
| game_type | int8 | 游戏类型：1-百家乐;2-龙虎斗;3-百家乐免佣;4-牛牛;5-三公;6-A89;7-庄闲牛;8-骰宝;9-轮盘 |
| game_type_name | string | 游戏类型名称 |
| stats | int8 | 当前状态：1-销售中;2-等待洗牌;3-等待出码;4-已收盘 |
| stats_name | string | 状态名称 |
| shoe_no | int | 场次编号（从0开始） |
| hand_no | int | 局号编号（从0开始） |
| create_time | string | 创建时间（格式：YYYY-MM-DD HH:mm:ss） |

## 使用示例

### JavaScript客户端示例

```javascript
// 建立WebSocket连接
const ws = new WebSocket('ws://localhost:8080/ws');

ws.onopen = function() {
    console.log('WebSocket连接已建立');
    
    // 用户登录
    ws.send(JSON.stringify({
        type: 'login',
        data: {
            username: 'admin',
            password: '123456'
        }
    }));
};

ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    
    switch(message.type) {
        case 'login_success':
            console.log('登录成功:', message.data.user_info);
            // 保存令牌
            localStorage.setItem('token', message.data.token);
            
            // 获取桌台信息
            ws.send(JSON.stringify({
                type: 'get_table_info'
            }));
            break;
            
        case 'login_error':
            console.error('登录失败:', message.data.error);
            break;
            
        case 'table_info_success':
            console.log('桌台信息获取成功:', message.data.table);
            break;
            
        case 'table_info_error':
            console.error('桌台信息获取失败:', message.data.error);
            break;
            
        case 'user_info_success':
            console.log('用户信息:', message.data.user_info);
            break;
            
        case 'change_password_success':
            console.log('密码修改成功');
            break;
            
        case 'verify_token_success':
            console.log('令牌验证成功:', message.data);
            break;
            
        case 'get_user_by_wash_code_success':
            console.log('客户信息获取成功:', message.data.user_info);
            break;
            
        case 'get_user_by_wash_code_error':
            console.error('客户信息获取失败:', message.data.error);
            break;
            
        case 'apply_out_code_success':
            console.log('出码申请提交成功:', message.data.out_code_list);
            break;
            
        case 'apply_out_code_error':
            console.error('出码申请失败:', message.data.error);
            break;
            
        case 'get_out_code_list_success':
            console.log('出码申请列表获取成功:', message.data.list);
            break;
            
        case 'get_out_code_list_error':
            console.error('出码申请列表获取失败:', message.data.error);
            break;
            
        case 'get_out_code_detail_success':
            console.log('出码申请详情获取成功:', message.data.out_code_info);
            break;
            
        case 'get_out_code_detail_error':
            console.error('出码申请详情获取失败:', message.data.error);
            break;
            
        case 'start_shuffle_success':
            console.log('洗牌开场成功:', message.data);
            break;
            
        case 'start_shuffle_error':
            console.error('洗牌开场失败:', message.data.error);
            break;
            
        case 'get_sys_user_by_serial_success':
            console.log('系统用户查询成功:', message.data);
            break;
            
        case 'get_sys_user_by_serial_error':
            console.error('系统用户查询失败:', message.data.error);
            break;
            
        case 'get_code_summary_success':
            console.log('点码查询成功:', message.data);
            break;
            
        case 'get_code_summary_error':
            console.error('点码查询失败:', message.data.error);
            break;
            

            
        case 'enter_result_success':
            console.log('百家乐结果录入成功:', message.data.message);
            break;
            
        case 'enter_result_error':
            console.error('百家乐结果录入失败:', message.data.error);
            break;
            
        case 'batch_settlement_success':
            console.log('批量点码成功:', message.data.message);
            break;
            
        case 'batch_settlement_error':
            console.error('批量点码失败:', message.data.error);
            break;
            
        case 'close_table_success':
            console.log('收盘成功:', message.data.tables_start);
            break;
            
        case 'close_table_error':
            console.error('收盘失败:', message.data.error);
            break;
            
    }
};

// 修改密码示例
function changePassword(oldPassword, newPassword) {
    ws.send(JSON.stringify({
        type: 'change_password',
        data: {
            old_password: oldPassword,
            new_password: newPassword
        }
    }));
}

// 获取用户信息示例
function getUserInfo() {
    ws.send(JSON.stringify({
        type: 'get_user_info'
    }));
}

// 验证令牌示例
function verifyToken(token) {
    ws.send(JSON.stringify({
        type: 'verify_token',
        data: {
            token: token
        }
    }));
}

// 登出示例
function logout() {
    ws.send(JSON.stringify({
        type: 'logout'
    }));
    localStorage.removeItem('token');
}

// 获取桌台信息示例
function getTableInfo() {
    ws.send(JSON.stringify({
        type: 'get_table_info'
    }));
}

// 获取客户信息示例
function getUserByWashCode(washCode) {
    ws.send(JSON.stringify({
        type: 'get_user_by_wash_code',
        data: {
            wash_code: washCode
        }
    }));
}

// 申请出码示例 - 批量申请
function applyOutCode(tableId, accountPeriod, amounts) {
    ws.send(JSON.stringify({
        type: 'apply_out_code',
        data: {
            table_id: tableId,
            account_period: accountPeriod,
            amounts: amounts
        }
    }));
}

// 示例调用：同时申请三种货币类型
applyOutCode(1, "2024-01-15", [
    { currency_type: 1, total_amount: 50000.00 }, // 筹码
    { currency_type: 2, total_amount: 30000.00 }, // 现金
    { currency_type: 3, total_amount: 20000.00 }  // U码
]);

// 洗牌开场示例
function startShuffle(tableId, accountPeriod, shift, shuffleData) {
    ws.send(JSON.stringify({
        type: 'start_shuffle',
        data: {
            table_id: tableId,
            account_period: accountPeriod,
            shift: shift,
            shuffle_method: shuffleData.shuffle_method || '',
            card_color: shuffleData.card_color || '',
            monitor_id: shuffleData.monitor_id || '',
            monitor_name: shuffleData.monitor_name || '',
            admin_id: shuffleData.admin_id || '',
            admin_name: shuffleData.admin_name || '',
            shuffle_table_poker: shuffleData.shuffle_table_poker || '',
            table_poker: shuffleData.table_poker || '',
            monitor_poker: shuffleData.monitor_poker || '',
            cut_card_dealer: shuffleData.cut_card_dealer || ''
        }
    }));
}

// 示例调用：开始洗牌
startShuffle(1, "2024-01-15", 1, {
    shuffle_method: "手动洗牌",
    card_color: "红色",
    monitor_id: "M001",
    monitor_name: "张监场",
    admin_id: "A001",
    admin_name: "王管理",
    shuffle_table_poker: "李洗牌",
    table_poker: "刘台手",
    monitor_poker: "陈监牌",
    cut_card_dealer: "赵切牌"
});

// 通过员工编号查询系统用户示例
function getSysUserBySerial(serialNumber) {
    ws.send(JSON.stringify({
        type: 'get_sys_user_by_serial',
        data: {
            serial_number: serialNumber
        }
    }));
}

// 示例调用：查询员工编号为1001的用户
getSysUserBySerial(1001);

// 获取出码申请列表示例
function getOutCodeList(tableId = 0, status = 0, limit = 20, offset = 0) {
    ws.send(JSON.stringify({
        type: 'get_out_code_list',
        data: {
            table_id: tableId,
            status: status,
            limit: limit,
            offset: offset
        }
    }));
}

// 获取出码申请详情示例
function getOutCodeDetail(id) {
    ws.send(JSON.stringify({
        type: 'get_out_code_detail',
        data: {
            id: id
        }
    }));
}

// 点码查询示例
function getCodeSummary(tableId, accountPeriod) {
    ws.send(JSON.stringify({
        type: 'get_code_summary',
        data: {
            table_id: tableId,
            account_period: accountPeriod
        }
    }));
}

// 示例调用：查询桌台1在202406账期的点码汇总
getCodeSummary(1, "202406");



// 百家乐结果录入示例
function enterResult(tableId, accountPeriod, roundNo, handNo, result) {
    ws.send(JSON.stringify({
        type: 'enter_result',
        data: {
            table_id: tableId,
            account_period: accountPeriod,
            round_no: roundNo,
            hand_no: handNo,
            result: result
        }
    }));
}

// 示例调用：录入百家乐结果
enterResult(1, "202406", 1, 1, ["庄", "庄对"]);

// 批量点码示例
function batchSettlement(settlementRecords) {
    ws.send(JSON.stringify({
        type: 'batch_settlement',
        data: {
            settlement_records: settlementRecords
        }
    }));
}

// 示例调用：批量录入点码记录
batchSettlement([
    {
        table_id: 1,
        shoe_no: 1,
        account_period: "202406",
        currency_type: 1,
        chips_20w: 2,
        chips_10w: 5,
        chips_5w: 10,
        chips_1w: 20,
        chips_5k: 50,
        chips_1k: 100,
        chips_500: 200,
        chips_100: 500,
        chips_50: 1000,
        chips_10: 5000,
        chips_5: 10000,
        total_amount: 1000000.00,
        client_win_loss: 50000.00,
        compare_result: "点码与注单比对正常",
        remark: "第一场次点码",
        operator: "张三"
    },
    {
        table_id: 1,
        shoe_no: 1,
        account_period: "202406",
        currency_type: 2,
        chips_20w: 1,
        chips_10w: 3,
        chips_5w: 5,
        chips_1w: 10,
        chips_5k: 20,
        chips_1k: 50,
        chips_500: 100,
        chips_100: 200,
        chips_50: 500,
        chips_10: 1000,
        chips_5: 2000,
        total_amount: 500000.00,
        client_win_loss: 25000.00,
        compare_result: "点码与注单比对正常",
        remark: "第一场次点码",
        operator: "张三"
    }
]);

// 收盘示例
function closeTable(tableId) {
    ws.send(JSON.stringify({
        type: 'close_table',
        data: {
            table_id: tableId
        }
    }));
}

// 示例调用：收盘
closeTable(1);

ws.onerror = function(error) {
    console.error('WebSocket错误:', error);
};

ws.onclose = function() {
    console.log('WebSocket连接已关闭');
};
```

## 错误码说明

### 认证错误

| 错误信息 | 说明 | 解决方案 |
|----------|------|----------|
| 用户名不能为空 | 登录时用户名为空 | 检查用户名参数 |
| 密码不能为空 | 登录时密码为空 | 检查密码参数 |
| 用户名或密码错误 | 用户名不存在或密码错误 | 检查用户名和密码是否正确 |
| 账号已被禁用，请联系管理员 | 用户账号被禁用 | 联系管理员启用账号 |
| 用户未登录 | 访问需要认证的接口时未登录 | 先进行登录 |
| 令牌无效，请重新登录 | JWT令牌过期或无效 | 重新登录获取新令牌 |
| 原密码错误 | 修改密码时原密码错误 | 检查原密码是否正确 |
| 新密码长度不能少于6位 | 新密码长度不足 | 设置至少6位的新密码 |

### 桌台信息错误

| 错误信息 | 说明 | 解决方案 |
|----------|------|----------|
| IP地址不能为空 | 请求的IP地址为空 | 检查IP地址参数 |
| 未找到IP为 xxx 的启用桌台 | 指定IP没有对应的启用桌台 | 检查IP地址是否正确，或桌台是否已启用 |
| 查询桌台信息失败 | 数据库查询错误 | 检查数据库连接和表结构 |
| 查询桌台下注配置失败 | 下注配置查询错误 | 检查数据库连接和表结构 |

## 认证流程说明

1. **建立连接**：客户端建立WebSocket连接
2. **用户登录**：发送login消息进行身份验证
3. **获取令牌**：登录成功后获得JWT令牌
4. **访问接口**：使用令牌访问需要认证的接口
5. **令牌刷新**：令牌过期后需要重新登录
6. **安全登出**：主动发送logout消息清除认证状态

## 安全建议

1. **令牌存储**：客户端应安全存储JWT令牌，避免XSS攻击
2. **HTTPS连接**：生产环境建议使用WSS（WebSocket over TLS）
3. **密码策略**：密码应包含字母、数字和特殊字符，长度至少8位
4. **会话管理**：及时清理过期的客户端连接
5. **访问控制**：根据用户角色限制接口访问权限 