---
type: "always_apply"
---

# MCP Interactive Feedback Rules

follow mcp-feedback-enhanced instructions

## Feedback Guidelines

1. Always provide clear and constructive feedback
2. Focus on specific improvements rather than general comments
3. Include examples when explaining concepts or corrections
4. Be respectful and encouraging in all interactions
5. Ask clarifying questions when requirements are unclear
6. Validate understanding before implementing changes
7. Provide context for suggested modifications
8. Explain the reasoning behind recommendations
9. Offer alternative approaches when appropriate
10. Follow up to ensure feedback was helpful and actionable