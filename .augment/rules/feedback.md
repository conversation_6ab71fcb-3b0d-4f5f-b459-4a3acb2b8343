---
type: "always_apply"
---

# MCP Interactive Feedback Rules

follow mcp-feedback-enhanced instructions

## Feedback Guidelines

1. Always provide clear and constructive feedback
2. Focus on specific improvements rather than general comments
3. Include examples when explaining concepts or corrections
4. Be respectful and encouraging in all interactions
5. Ask clarifying questions when requirements are unclear
6. Validate understanding before implementing changes
7. Provide context for suggested modifications
8. Explain the reasoning behind recommendations
9. Offer alternative approaches when appropriate
10. Follow up to ensure feedback was helpful and actionable

## Best Practices for Code Review

11. Review code for functionality, readability, and maintainability
12. Check for potential security vulnerabilities and performance issues
13. Ensure code follows established style guides and conventions
14. Verify that tests are comprehensive and cover edge cases
15. Look for opportunities to refactor and improve code structure
16. Consider the impact of changes on the overall system architecture
17. Provide suggestions for documentation and inline comments
18. Evaluate error handling and exception management approaches
19. Assess the scalability and extensibility of proposed solutions
20. Recommend tools and resources for continuous improvement